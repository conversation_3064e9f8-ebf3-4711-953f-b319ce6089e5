# 开发规范和规则

- 管理后台功能优化要求：教师管理增强（任教学校多选、所教年级多选、任课班级动态显示、重置密码、删除功能），学校管理简化（只保留名称字段），学生管理增强（所属学校、任课教师匹配），移除班级管理和权限分配菜单，所有操作使用Bootstrap模态框，实时同步Supabase数据库
- 用户要求修改打字管理功能：1.将URL后缀改为/type 2.将数据库从MySQL迁移到Supabase 3.在筛选条件中添加学校选项 4.实现教师权限控制，只能查看本人所教学生的打字成绩，逻辑与奖章管理一致
- 用户要求删除打字管理中的增加、减少功能，学生打字速度不需要教师手动增减，教师只需要查看数据即可
- 用户要求：1.优化筛选功能用户体验，减少每次点击都显示加载中的问题 2.创建学生端独立HTML页面，URL为/stusign（签到界面）和/stutype（打字练习界面），从原有index.html中分离学生功能
- 教师端学生管理功能修复：1.修复API返回数据格式不一致问题（统一返回数组格式）2.增强前端数据字段兼容性处理（支持多种字段名称）3.添加详细的调试日志帮助诊断权限问题 4.创建权限修复脚本fix_teacher_permissions.sql自动分配教师权限 5.创建API测试脚本test_teacher_api.js验证功能 6.修复编辑功能中的数据字段映射问题
- 教师端学生管理显示问题：添加学生使用teacher_class_permissions表权限检查，获取学生列表使用teacher_school_assignments表权限过滤，两表数据不同步导致添加的学生无法显示。需要统一权限查询逻辑或确保两表数据一致性。
- 管理员教师管理功能完善：1.在教师列表中显示每个教师的任教学校和年级班级信息 2.在编辑教师时可以配置和修改任教信息（学校、年级、班级） 3.显示教师自己已配置的权限，并允许管理员查看和修改 4.所有功能都需要实现
