/**
 * 奖章控制器
 * 处理所有与奖章相关的请求
 * 支持教师权限控制和学校筛选功能
 */

const db = require('../config/db');

/**
 * 获取学生奖章信息（支持教师权限控制）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getMedals = async (req, res) => {
  try {
    const { student_identifier, grade, class: className, search, school_id } = req.query;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    console.log(`用户 ${req.user.username} (${userRole}) 请求奖章数据`);

    // 如果提供了学生标识符，则查询单个学生的奖章
    if (student_identifier) {
      // 检查学生是否存在
      const { data: student, error: studentError } = await db.supabase
        .from('students')
        .select('*')
        .eq('student_identifier', student_identifier)
        .single();

      if (studentError || !student) {
        return res.status(404).json({ error: '学生不存在' });
      }

      // 教师权限检查：只能查看自己任教班级的学生
      if (userRole !== 'admin') {
        const { data: hasPermission } = await db.supabase
          .from('teacher_class_permissions')
          .select('id')
          .eq('teacher_id', userId)
          .eq('school_id', student.school_id)
          .eq('grade', student.grade)
          .eq('class', student.class)
          .single();

        if (!hasPermission) {
          return res.status(403).json({ error: '您没有权限查看该学生的奖章信息' });
        }
      }

      // 获取奖章信息
      const { data: medal, error: medalError } = await db.supabase
        .from('medals')
        .select('*')
        .eq('student_identifier', student_identifier)
        .single();

      // 如果没有奖章记录，创建一个
      if (medalError || !medal) {
        const { data: newMedal, error: insertError } = await db.supabase
          .from('medals')
          .insert({ student_identifier, count: 0 })
          .select()
          .single();

        if (insertError) {
          console.error('创建奖章记录失败:', insertError);
          return res.status(500).json({ error: '创建奖章记录失败' });
        }

        return res.status(200).json({ data: newMedal });
      }

      return res.status(200).json({ data: medal });
    }

    // 查询所有学生的奖章数据，支持教师权限控制
    let query = db.supabase
      .from('students')
      .select(`
        *,
        medals!left(count),
        schools!left(name)
      `);

    // 教师权限控制：只能查看自己任教班级的学生
    if (userRole !== 'admin') {
      // 获取教师有权限的班级
      const { data: permissions, error: permError } = await db.supabase
        .from('teacher_class_permissions')
        .select('school_id, grade, class')
        .eq('teacher_id', userId);

      if (permError) {
        console.error('获取教师权限失败:', permError);
        return res.status(500).json({ error: '获取权限信息失败' });
      }

      if (!permissions || permissions.length === 0) {
        console.log(`教师 ${req.user.username} 没有任何班级权限`);
        return res.status(200).json({ data: [] });
      }

      console.log(`教师 ${req.user.username} 的权限:`, permissions);

      // 构建权限过滤条件 - 使用 OR 条件组合
      const orConditions = permissions.map(p =>
        `and(school_id.eq.${p.school_id},grade.eq.${p.grade},class.eq.${p.class})`
      ).join(',');

      query = query.or(orConditions);
    } else {
      console.log(`管理员 ${req.user.username} 请求所有学生奖章数据`);
    }

    // 添加筛选条件
    if (school_id) {
      query = query.eq('school_id', school_id);
    }

    if (grade) {
      query = query.eq('grade', grade);
    }

    if (className) {
      query = query.eq('class', className);
    }

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // 执行查询并排序
    query = query.order('grade').order('class').order('name');

    const { data: students, error } = await query;

    if (error) {
      console.error('获取奖章数据失败:', error);
      return res.status(500).json({ error: '获取奖章数据失败: ' + error.message });
    }

    // 调试：打印原始数据结构
    console.log('原始学生数据示例:', JSON.stringify(students?.[0], null, 2));

    // 处理数据格式，确保奖章数量字段正确
    const processedData = students.map(student => {
      // 处理奖章数据 - 如果没有奖章记录，默认为0
      let medalCount = 0;

      // 调试：打印每个学生的奖章数据结构
      console.log(`学生 ${student.name} 的奖章数据:`, student.medals);

      if (student.medals) {
        if (Array.isArray(student.medals) && student.medals.length > 0 && student.medals[0] !== null) {
          medalCount = student.medals[0].count || 0;
        } else if (typeof student.medals === 'object' && student.medals.count !== undefined) {
          // 如果medals不是数组而是对象
          medalCount = student.medals.count || 0;
        }
      }

      const result = {
        ...student,
        medal_count: medalCount,
        school_name: student.schools?.name || '未知学校'
      };

      console.log(`学生 ${student.name} 处理后的奖章数量:`, medalCount);
      return result;
    });

    // 返回学生及其奖章数据
    res.status(200).json({ data: processedData });
  } catch (error) {
    console.error('获取奖章信息错误:', error);
    res.status(500).json({ error: '获取奖章信息失败: ' + error.message });
  }
};

/**
 * 更新学生奖章数量
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.updateMedal = async (req, res) => {
  try {
    const { student_id, count, student_identifier, increment } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    // 支持使用student_id或student_identifier
    const studentId = student_identifier || student_id;

    // 验证必填字段
    if (!studentId) {
      return res.status(400).json({ error: '学生标识符为必填项' });
    }

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    // 检查学生是否存在
    const { data: student, error: studentError } = await db.supabase
      .from('students')
      .select('*')
      .eq('student_identifier', studentId)
      .single();

    if (studentError || !student) {
      return res.status(404).json({ error: '学生不存在' });
    }

    // 教师权限检查：只能修改自己任教班级的学生奖章
    if (userRole !== 'admin') {
      const { data: hasPermission } = await db.supabase
        .from('teacher_class_permissions')
        .select('id')
        .eq('teacher_id', userId)
        .eq('school_id', student.school_id)
        .eq('grade', student.grade)
        .eq('class', student.class)
        .single();

      if (!hasPermission) {
        return res.status(403).json({ error: '您没有权限修改该学生的奖章数量' });
      }
    }

    // 获取当前奖章数量
    const { data: currentMedal } = await db.supabase
      .from('medals')
      .select('*')
      .eq('student_identifier', studentId)
      .single();

    let currentCount = 0;
    let medalExists = !!currentMedal;

    if (medalExists) {
      currentCount = currentMedal.count;
    }

    // 计算新的奖章数量
    let newCount = currentCount;
    if (increment !== undefined) {
      newCount = Math.max(0, currentCount + parseInt(increment)); // 确保数量不为负
    } else if (count !== undefined) {
      newCount = Math.max(0, parseInt(count)); // 直接设置数量，确保不为负
    }

    // 更新或创建奖章记录
    let result;
    if (medalExists) {
      const { data, error } = await db.supabase
        .from('medals')
        .update({ count: newCount })
        .eq('student_identifier', studentId)
        .select()
        .single();

      if (error) {
        console.error('更新奖章记录失败:', error);
        return res.status(500).json({ error: '更新奖章记录失败' });
      }
      result = data;
    } else {
      const { data, error } = await db.supabase
        .from('medals')
        .insert({ student_identifier: studentId, count: newCount })
        .select()
        .single();

      if (error) {
        console.error('创建奖章记录失败:', error);
        return res.status(500).json({ error: '创建奖章记录失败' });
      }
      result = data;
    }

    res.status(200).json({ data: result });
  } catch (error) {
    console.error('更新奖章错误:', error);
    res.status(500).json({ error: '更新奖章失败: ' + error.message });
  }
};

/**
 * 批量更新学生奖章（支持教师权限控制）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.batchUpdateMedals = async (req, res) => {
  try {
    const { student_identifiers, increment } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    // 验证必填字段
    if (!student_identifiers || !Array.isArray(student_identifiers) || student_identifiers.length === 0) {
      return res.status(400).json({ error: '学生标识符数组为必填项' });
    }

    if (increment === undefined || isNaN(parseInt(increment))) {
      return res.status(400).json({ error: '增量值必须是有效数字' });
    }

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    // 获取所有相关学生信息
    const { data: students, error: studentsError } = await db.supabase
      .from('students')
      .select('*')
      .in('student_identifier', student_identifiers);

    if (studentsError) {
      console.error('获取学生信息失败:', studentsError);
      return res.status(500).json({ error: '获取学生信息失败' });
    }

    // 教师权限检查：验证所有学生都在教师任教班级内
    if (userRole !== 'admin') {
      const { data: permissions, error: permError } = await db.supabase
        .from('teacher_class_permissions')
        .select('school_id, grade, class')
        .eq('teacher_id', userId);

      if (permError || !permissions || permissions.length === 0) {
        return res.status(403).json({ error: '您没有任何班级的管理权限' });
      }

      // 检查每个学生是否在教师权限范围内
      for (const student of students) {
        const hasPermission = permissions.some(p =>
          p.school_id === student.school_id &&
          p.grade === student.grade &&
          p.class === student.class
        );

        if (!hasPermission) {
          return res.status(403).json({
            error: `您没有权限修改学生 ${student.name} 的奖章数量`
          });
        }
      }
    }

    // 批量更新奖章
    const updatePromises = student_identifiers.map(async (studentId) => {
      // 获取当前奖章数量
      const { data: currentMedal } = await db.supabase
        .from('medals')
        .select('*')
        .eq('student_identifier', studentId)
        .single();

      let currentCount = 0;
      let medalExists = !!currentMedal;

      if (medalExists) {
        currentCount = currentMedal.count;
      }

      // 计算新的奖章数量（确保不为负）
      const newCount = Math.max(0, currentCount + parseInt(increment));

      // 更新或创建奖章记录
      if (medalExists) {
        return db.supabase
          .from('medals')
          .update({ count: newCount })
          .eq('student_identifier', studentId);
      } else {
        return db.supabase
          .from('medals')
          .insert({ student_identifier: studentId, count: newCount });
      }
    });

    // 执行所有更新操作
    const results = await Promise.all(updatePromises);

    // 检查是否有错误
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      console.error('批量更新奖章时出现错误:', errors);
      return res.status(500).json({ error: '部分奖章更新失败' });
    }

    // 获取更新后的学生数据
    const { data: updatedStudents, error: fetchError } = await db.supabase
      .from('students')
      .select(`
        *,
        medals(count),
        typing_records(speed, accuracy)
      `)
      .in('student_identifier', student_identifiers);

    if (fetchError) {
      console.error('获取更新后数据失败:', fetchError);
      return res.status(500).json({ error: '获取更新后数据失败' });
    }

    // 处理数据格式
    const processedStudents = updatedStudents.map(student => ({
      ...student,
      medal_count: student.medals?.[0]?.count || 0,
      best_speed: Math.max(...(student.typing_records?.map(r => r.speed) || [0])),
      best_accuracy: Math.max(...(student.typing_records?.map(r => r.accuracy) || [0]))
    }));

    res.status(200).json({
      data: {
        message: `已为${student_identifiers.length}个学生更新奖章`,
        students: processedStudents
      }
    });
  } catch (error) {
    console.error('批量更新奖章错误:', error);
    res.status(500).json({ error: '批量更新奖章失败: ' + error.message });
  }
};