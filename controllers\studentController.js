/**
 * 学生控制器
 * 处理所有与学生相关的请求
 */

const db = require('../config/db');
const XLSX = require('xlsx');
const multer = require('multer');
const path = require('path');

// 配置multer用于文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.xlsx', '.xls'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式(.xlsx, .xls)'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB限制
  }
});

/**
 * 获取当前用户可见的学生列表（带权限控制）
 * 管理员可以查看所有学生，教师只能查看自己任教的班级
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getAllStudents = async (req, res) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    console.log(`用户 ${req.user.username} (${userRole}) 请求学生列表`);

    let students;

    if (userRole === 'admin') {
      // 管理员可以查看所有学生
      const { data, error } = await db.supabase
        .from('students')
        .select(`
          *,
          medals(count),
          typing_records(speed, accuracy)
        `)
        .order('grade')
        .order('class')
        .order('name');

      if (error) throw error;
      students = data;

    } else {
      // 普通教师只能查看自己任教的班级
      const { data, error } = await db.supabase
        .rpc('get_teacher_students', { p_teacher_id: userId });

      if (error) throw error;

      // 为每个学生获取奖章和打字记录
      const studentIds = data.map(s => s.student_identifier);

      if (studentIds.length > 0) {
        // 获取奖章数据
        const { data: medals, error: medalsError } = await db.supabase
          .from('medals')
          .select('student_identifier, count')
          .in('student_identifier', studentIds);

        if (medalsError) throw medalsError;

        // 获取打字记录
        const { data: typingRecords, error: typingError } = await db.supabase
          .from('typing_records')
          .select('student_identifier, speed, accuracy')
          .in('student_identifier', studentIds);

        if (typingError) throw typingError;

        // 合并数据
        students = data.map(student => ({
          ...student,
          medals: medals.filter(m => m.student_identifier === student.student_identifier),
          typing_records: typingRecords.filter(t => t.student_identifier === student.student_identifier)
        }));
      } else {
        students = [];
      }
    }

    // 处理数据，计算最佳成绩和奖章数量
    const processedStudents = students.map(student => ({
      ...student,
      medal_count: student.medals?.[0]?.count || 0,
      best_speed: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.speed))
        : 0,
      best_accuracy: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.accuracy))
        : 0
    }));

    console.log(`返回 ${processedStudents.length} 个学生记录给用户 ${req.user.username}`);

    res.status(200).json({
      data: processedStudents,
      total: processedStudents.length,
      user_role: userRole
    });
  } catch (error) {
    console.error('获取学生列表错误:', error);
    res.status(500).json({ error: '获取学生列表失败: ' + error.message });
  }
};

/**
 * 获取单个学生
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getStudent = async (req, res) => {
  try {
    const studentId = req.params.id;

    const { data: student, error } = await db.supabase
      .from('students')
      .select(`
        *,
        medals(count),
        typing_records(speed, accuracy)
      `)
      .eq('student_identifier', studentId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return res.status(404).json({ error: '学生不存在' });
      }
      throw error;
    }

    // 处理数据
    const processedStudent = {
      ...student,
      medal_count: student.medals?.[0]?.count || 0,
      best_speed: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.speed))
        : 0,
      best_accuracy: student.typing_records?.length > 0
        ? Math.max(...student.typing_records.map(r => r.accuracy))
        : 0
    };

    res.status(200).json({ data: processedStudent });
  } catch (error) {
    console.error('获取学生信息错误:', error);
    res.status(500).json({ error: '获取学生信息失败: ' + error.message });
  }
};

/**
 * 创建学生
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.createStudent = async (req, res) => {
  try {
    const { name, student_id, grade, class: className, school_id, group_number, gender, seat_number } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    // 验证必填字段
    if (!name || !grade || !className) {
      return res.status(400).json({ error: '姓名、年级和班级为必填项' });
    }

    // 生成学生标识符（如果没有提供）
    let studentIdentifier = student_id?.trim();
    if (!studentIdentifier || studentIdentifier === '') {
      // 生成简短的学号：年级(1位) + 班级(2位) + 随机数(4位)
      const randomNum = Math.floor(Math.random() * 9000) + 1000; // 1000-9999
      studentIdentifier = `${grade}${String(className).padStart(2, '0')}${randomNum}`;

      // 确保学号唯一性，如果重复则重新生成
      let attempts = 0;
      while (attempts < 10) {
        const { data: existing } = await db.supabase
          .from('students')
          .select('id')
          .eq('student_identifier', studentIdentifier)
          .single();

        if (!existing) break;

        const newRandomNum = Math.floor(Math.random() * 9000) + 1000;
        studentIdentifier = `${grade}${String(className).padStart(2, '0')}${newRandomNum}`;
        attempts++;
      }
    }

    // 检查学生标识符是否已存在
    const { data: existingStudent } = await db.supabase
      .from('students')
      .select('student_identifier')
      .eq('student_identifier', studentIdentifier)
      .single();

    if (existingStudent) {
      return res.status(400).json({ error: '学号已存在，请使用其他学号' });
    }

    // 准备插入数据
    const insertData = {
      student_identifier: studentIdentifier,
      name: name.trim(),
      grade: parseInt(grade),
      class: parseInt(className),
      school_id: school_id ? parseInt(school_id) : null,
      status: 'active',
      created_at: new Date().toISOString()
    };

    // 添加可选字段
    if (group_number && !isNaN(parseInt(group_number))) {
      insertData.group_number = parseInt(group_number);
    }

    if (gender && gender.trim()) {
      insertData.gender = gender.trim();
    }

    if (seat_number && !isNaN(parseInt(seat_number))) {
      insertData.seat_number = parseInt(seat_number);
    }

    // 权限检查：教师只能在自己有权限的班级添加学生
    if (userRole === 'teacher' && school_id) {
      const hasPermission = await checkTeacherClassPermission(userId, school_id, grade, className);
      if (!hasPermission) {
        return res.status(403).json({ error: '无权限在该班级添加学生' });
      }
    }

    // 插入新学生
    const { data: newStudent, error: insertError } = await db.supabase
      .from('students')
      .insert([insertData])
      .select(`
        *,
        schools(id, name)
      `)
      .single();

    if (insertError) {
      throw insertError;
    }

    // 创建奖章记录（可选）
    try {
      await db.supabase
        .from('medals')
        .insert({
          student_identifier: studentIdentifier,
          count: 0
        });
    } catch (medalError) {
      console.error('创建奖章记录失败:', medalError);
      // 不抛出错误，因为学生已创建成功
    }

    console.log('成功添加学生:', newStudent);
    res.status(201).json({
      success: true,
      message: '学生添加成功',
      data: newStudent
    });

  } catch (error) {
    console.error('创建学生错误:', error);
    res.status(500).json({ error: '创建学生失败: ' + error.message });
  }
};

/**
 * 检查教师是否有班级权限
 */
async function checkTeacherClassPermission(teacherId, schoolId, grade, className) {
  try {
    const { data: permissions, error } = await db.supabase
      .from('teacher_class_permissions')
      .select('*')
      .eq('teacher_id', teacherId)
      .eq('school_id', schoolId)
      .eq('grade', grade)
      .eq('class', className);

    return !error && permissions && permissions.length > 0;
  } catch (error) {
    console.error('检查教师班级权限失败:', error);
    return false;
  }
}

/**
 * 获取单个学生信息
 */
exports.getStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    const { data: student, error } = await db.supabase
      .from('students')
      .select(`
        *,
        schools(id, name)
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('获取学生信息失败:', error);
      return res.status(404).json({ error: '学生不存在' });
    }

    // 权限检查：管理员可以查看所有学生，教师只能查看自己任教的班级
    if (userRole !== 'admin') {
      const hasPermission = await checkTeacherStudentPermission(userId, student);
      if (!hasPermission) {
        return res.status(403).json({ error: '无权限查看该学生信息' });
      }
    }

    res.json({
      success: true,
      data: student
    });
  } catch (error) {
    console.error('获取学生信息失败:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
};

/**
 * 更新学生信息（完善版）
 */
exports.updateStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, grade, class: studentClass, school_id, group_number, gender, seat_number } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    if (!name || !grade || !studentClass) {
      return res.status(400).json({ error: '姓名、年级和班级为必填项' });
    }

    // 获取原学生信息
    const { data: originalStudent, error: fetchError } = await db.supabase
      .from('students')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !originalStudent) {
      return res.status(404).json({ error: '学生不存在' });
    }

    // 权限检查：管理员可以编辑所有学生，教师只能编辑自己任教的班级
    if (userRole !== 'admin') {
      const hasPermission = await checkTeacherStudentPermission(userId, originalStudent);
      if (!hasPermission) {
        return res.status(403).json({ error: '无权限编辑该学生信息' });
      }
    }

    // 准备更新数据
    const updateData = {
      name: name.trim(),
      grade: parseInt(grade),
      class: parseInt(studentClass),
      school_id: school_id ? parseInt(school_id) : null,
      updated_at: new Date().toISOString()
    };

    // 添加可选字段
    if (group_number !== undefined && group_number !== '') {
      updateData.group_number = group_number ? parseInt(group_number) : null;
    }

    if (gender !== undefined) {
      updateData.gender = gender ? gender.trim() : null;
    }

    if (seat_number !== undefined && seat_number !== '') {
      updateData.seat_number = seat_number ? parseInt(seat_number) : null;
    }

    // 更新学生信息
    const { data: updatedStudent, error: updateError } = await db.supabase
      .from('students')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        schools(id, name)
      `)
      .single();

    if (updateError) {
      console.error('更新学生信息失败:', updateError);
      return res.status(500).json({ error: '更新学生信息失败' });
    }

    console.log('成功更新学生信息:', updatedStudent);
    res.json({
      success: true,
      message: '学生信息更新成功',
      data: updatedStudent
    });

  } catch (error) {
    console.error('更新学生信息失败:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
};

/**
 * 删除学生（硬删除）
 */
exports.deleteStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    // 获取学生信息
    const { data: student, error: fetchError } = await db.supabase
      .from('students')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !student) {
      return res.status(404).json({ error: '学生不存在' });
    }

    // 权限检查：管理员可以删除所有学生，教师只能删除自己任教的班级
    if (userRole !== 'admin') {
      const hasPermission = await checkTeacherStudentPermission(userId, student);
      if (!hasPermission) {
        return res.status(403).json({ error: '无权限删除该学生' });
      }
    }

    // 直接从数据库删除学生记录
    const { error: deleteError } = await db.supabase
      .from('students')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('删除学生失败:', deleteError);
      return res.status(500).json({ error: '删除学生失败' });
    }

    console.log('成功删除学生:', student.name);
    res.json({
      success: true,
      message: '学生删除成功'
    });

  } catch (error) {
    console.error('删除学生失败:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
};

/**
 * Excel批量导入学生
 */
exports.batchImportStudents = async (req, res) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    if (!req.file) {
      return res.status(400).json({ error: '请上传Excel文件' });
    }

    // 解析Excel文件
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    if (!jsonData || jsonData.length === 0) {
      return res.status(400).json({ error: 'Excel文件为空或格式不正确' });
    }

    console.log('解析到的Excel数据:', jsonData.slice(0, 3)); // 只打印前3条用于调试

    const results = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: [],
      created_schools: [],
      created_permissions: [],
      duplicate_students: [],
      processed_classes: new Set() // 记录处理过的班级
    };

    // 优化的批量导入流程
    console.log('开始优化的批量导入流程...');

    // 第一阶段：预处理和收集信息
    const schoolsToCreate = new Map();
    const classesToCreate = new Set();
    const validRows = [];

    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      const schoolName = (row['学校'] || row['school'] || row['学校名称'] || row['School'] || '').toString().trim();
      const grade = row['年级'] || row['grade'] || row['Grade'];
      const className = row['班级'] || row['class'] || row['Class'] || row['班'];

      if (schoolName && grade && className) {
        schoolsToCreate.set(schoolName, schoolName);
        classesToCreate.add(`${schoolName}-${grade}-${className}`);
        validRows.push({ row, index: i });
      } else {
        results.failed++;
        results.errors.push({
          row: i + 2,
          error: '缺少必填字段：学校、年级或班级',
          data: row
        });
      }
    }

    console.log(`预处理完成：${schoolsToCreate.size} 个学校，${classesToCreate.size} 个班级，${validRows.length} 个有效学生`);

    // 第二阶段：批量创建学校和教师关联
    if (userRole === 'teacher') {
      for (const schoolName of schoolsToCreate.values()) {
        try {
          const school = await findOrCreateSchool(schoolName, userId);
          if (school && school.is_new) {
            results.created_schools.push(school);
            console.log(`创建新学校: ${school.name} (ID: ${school.id})`);
          }
        } catch (error) {
          console.error(`创建学校失败: ${schoolName}`, error);
        }
      }

      // 第三阶段：批量创建班级权限
      for (const classKey of classesToCreate) {
        try {
          const [schoolName, grade, className] = classKey.split('-');
          const school = await findOrCreateSchool(schoolName, userId);
          if (school) {
            const permission = await createTeacherPermissionIfNeeded(userId, school.id, parseInt(grade), parseInt(className));
            if (permission && permission.is_new) {
              results.created_permissions.push(permission);
              console.log(`创建班级权限: ${schoolName} ${grade}年级${className}班`);
            }
          }
        } catch (error) {
          console.error(`创建班级权限失败: ${classKey}`, error);
        }
      }
    }

    console.log(`权限创建完成：${results.created_schools.length} 个新学校，${results.created_permissions.length} 个新权限`);

    // 第四阶段：优化的批量处理学生数据
    console.log('开始批量处理学生数据...');

    // 获取所有需要的学校信息（避免重复查询）
    const schoolMap = new Map();
    for (const schoolName of schoolsToCreate.values()) {
      const school = await findOrCreateSchool(schoolName, userRole === 'teacher' ? userId : null);
      if (school) {
        schoolMap.set(schoolName, school);
      }
    }

    // 批量检查现有学生（一次查询获取所有可能的重复）
    const allIdentifiers = [];
    const allNameSchoolGradeClass = [];

    for (const { row } of validRows) {
      const name = (row['姓名'] || row['name'] || '').toString().trim();
      const schoolName = (row['学校'] || row['school'] || '').toString().trim();
      const grade = parseInt(row['年级'] || row['grade'] || 0);
      const className = parseInt(row['班级'] || row['class'] || 0);
      const studentId = row['学号'] || row['student_id'] || '';

      const school = schoolMap.get(schoolName);
      if (school && name && grade && className) {
        // 生成学号（如果没有）
        let identifier = studentId;
        if (!identifier || identifier.toString().trim() === '') {
          const randomNum = Math.floor(Math.random() * 9000) + 1000;
          identifier = `${grade}${String(className).padStart(2, '0')}${randomNum}`;
        }

        allIdentifiers.push(identifier.toString());
        allNameSchoolGradeClass.push({
          name: name,
          school_id: school.id,
          grade: grade,
          class: className,
          identifier: identifier.toString()
        });
      }
    }

    // 一次性查询所有可能的重复学生
    const { data: existingStudents } = await db.supabase
      .from('students')
      .select('id, name, student_identifier, school_id, grade, class')
      .in('student_identifier', allIdentifiers);

    const existingMap = new Map();
    if (existingStudents) {
      existingStudents.forEach(student => {
        existingMap.set(student.student_identifier, student);
        existingMap.set(`${student.name}-${student.school_id}-${student.grade}-${student.class}`, student);
      });
    }

    // 处理学生数据
    const studentsToInsert = [];
    const duplicateChecks = new Map(); // 本次导入内部去重

    for (const { row, index } of validRows) {
      try {
        const name = (row['姓名'] || row['name'] || '').toString().trim();
        const schoolName = (row['学校'] || row['school'] || '').toString().trim();
        const grade = parseInt(row['年级'] || row['grade'] || 0);
        const className = parseInt(row['班级'] || row['class'] || 0);
        const studentId = row['学号'] || row['student_id'] || '';
        const groupNumber = row['组号'] || row['group'];
        const gender = (row['性别'] || row['gender'] || '').toString().trim();
        const seatNumber = row['座位号'] || row['seat'];

        // 验证必填字段
        if (!name || !schoolName || !grade || !className) {
          results.failed++;
          results.errors.push({
            row: index + 2,
            error: '缺少必填字段：姓名、学校、年级或班级',
            data: row
          });
          continue;
        }

        const school = schoolMap.get(schoolName);
        if (!school) {
          results.failed++;
          results.errors.push({
            row: index + 2,
            error: '无法找到或创建学校: ' + schoolName,
            data: row
          });
          continue;
        }

        // 生成学号
        let identifier = studentId;
        if (!identifier || identifier.toString().trim() === '') {
          const randomNum = Math.floor(Math.random() * 9000) + 1000;
          identifier = `${grade}${String(className).padStart(2, '0')}${randomNum}`;
        }
        identifier = identifier.toString();

        // 检查重复
        const duplicateKey = `${name}-${school.id}-${grade}-${className}`;

        // 检查数据库中是否已存在
        if (existingMap.has(identifier) || existingMap.has(duplicateKey)) {
          results.skipped++;
          results.duplicate_students.push({
            row: index + 2,
            student_identifier: identifier,
            name: name
          });
          continue;
        }

        // 检查本次导入内部是否重复
        if (duplicateChecks.has(duplicateKey) || duplicateChecks.has(identifier)) {
          results.skipped++;
          results.duplicate_students.push({
            row: index + 2,
            student_identifier: identifier,
            name: name
          });
          continue;
        }

        duplicateChecks.set(duplicateKey, true);
        duplicateChecks.set(identifier, true);

        // 准备学生数据
        const studentData = {
          student_identifier: identifier,
          name: name,
          grade: grade,
          class: className,
          school_id: school.id,
          status: 'active',
          created_at: new Date().toISOString()
        };

        // 添加可选字段
        if (groupNumber && !isNaN(parseInt(groupNumber))) {
          studentData.group_number = parseInt(groupNumber);
        }
        if (gender) {
          studentData.gender = gender;
        }
        if (seatNumber && !isNaN(parseInt(seatNumber))) {
          studentData.seat_number = parseInt(seatNumber);
        }

        studentsToInsert.push(studentData);

      } catch (error) {
        results.failed++;
        results.errors.push({
          row: index + 2,
          error: error.message,
          data: row
        });
      }
    }

    // 批量插入学生数据
    if (studentsToInsert.length > 0) {
      try {
        console.log(`准备批量插入 ${studentsToInsert.length} 个学生...`);
        const { data: insertedStudents, error: insertError } = await db.supabase
          .from('students')
          .insert(studentsToInsert)
          .select();

        if (insertError) {
          console.error('批量插入学生失败:', insertError);
          results.failed += studentsToInsert.length;
          results.errors.push({
            row: 'batch',
            error: '批量插入失败: ' + insertError.message,
            data: studentsToInsert
          });
        } else {
          results.success += insertedStudents.length;
          console.log(`批量插入成功: ${insertedStudents.length} 个学生`);
        }
      } catch (error) {
        console.error('批量插入异常:', error);
        results.failed += studentsToInsert.length;
        results.errors.push({
          row: 'batch',
          error: '批量插入异常: ' + error.message,
          data: studentsToInsert
        });
      }
    }

    console.log(`学生数据处理完成：成功 ${results.success}，失败 ${results.failed}，跳过 ${results.skipped}`);

    // 生成详细的导入报告
    let message = `批量导入完成：成功 ${results.success} 个`;
    if (results.skipped > 0) {
      message += `，跳过重复 ${results.skipped} 个`;
    }
    if (results.failed > 0) {
      message += `，失败 ${results.failed} 个`;
    }

    // 添加创建的资源信息
    if (results.created_schools.length > 0) {
      message += `。自动创建了 ${results.created_schools.length} 个学校`;
    }
    if (results.created_permissions.length > 0) {
      message += `。为您分配了 ${results.created_permissions.length} 个班级权限`;
    }

    // 转换processed_classes为数组
    results.processed_classes = Array.from(results.processed_classes);

    res.json({
      success: true,
      message: message,
      data: {
        ...results,
        total_processed: jsonData.length,
        summary: {
          success: results.success,
          failed: results.failed,
          skipped: results.skipped,
          created_schools: results.created_schools.length,
          created_permissions: results.created_permissions.length,
          processed_classes: results.processed_classes.length
        }
      }
    });

  } catch (error) {
    console.error('批量导入学生失败:', error);
    res.status(500).json({ error: '批量导入失败: ' + error.message });
  }
};



/**
 * 处理单行学生数据
 */
async function processStudentRow(row, userId, userRole, rowNumber, skipPermissionCreation = false) {
  try {
    // 解析字段（支持中英文列名）
    const name = row['姓名'] || row['name'] || row['学生姓名'] || row['Name'];
    const schoolName = row['学校'] || row['school'] || row['学校名称'] || row['School'];
    const grade = row['年级'] || row['grade'] || row['Grade'];
    const className = row['班级'] || row['class'] || row['Class'] || row['班'];
    const studentId = row['学号'] || row['student_id'] || row['学生标识符'] || row['StudentID'];
    const groupNumber = row['组号'] || row['group'] || row['小组'] || row['Group'];
    const gender = row['性别'] || row['gender'] || row['Gender'];
    const seatNumber = row['座位号'] || row['seat'] || row['Seat'];

    // 验证必填字段
    if (!name || (typeof name === 'string' && name.trim() === '')) {
      return {
        success: false,
        error: '姓名不能为空'
      };
    }

    if (!schoolName || (typeof schoolName === 'string' && schoolName.trim() === '')) {
      return {
        success: false,
        error: '学校名称不能为空'
      };
    }

    if (!grade || isNaN(parseInt(grade))) {
      return {
        success: false,
        error: '年级必须是有效数字'
      };
    }

    if (!className || isNaN(parseInt(className))) {
      return {
        success: false,
        error: '班级必须是有效数字'
      };
    }

    // 验证数据范围
    const gradeNum = parseInt(grade);
    const classNum = parseInt(className);

    if (gradeNum < 1 || gradeNum > 12) {
      return {
        success: false,
        error: '年级必须在1-12之间'
      };
    }

    if (classNum < 1 || classNum > 50) {
      return {
        success: false,
        error: '班级必须在1-50之间'
      };
    }

    // 生成学生标识符（如果没有提供）
    let identifier = studentId;

    // 修复：确保identifier是字符串类型，避免trim()错误
    if (!identifier || (typeof identifier === 'string' && identifier.trim() === '') || identifier === null || identifier === undefined) {
      // 生成简短的学号：年级(1位) + 班级(2位) + 随机数(4位)
      const randomNum = Math.floor(Math.random() * 9000) + 1000; // 1000-9999
      identifier = `${grade}${String(className).padStart(2, '0')}${randomNum}`;

      // 确保学号唯一性，如果重复则重新生成
      let attempts = 0;
      while (attempts < 10) {
        const { data: existing } = await db.supabase
          .from('students')
          .select('id')
          .eq('student_identifier', identifier)
          .single();

        if (!existing) break;

        const newRandomNum = Math.floor(Math.random() * 9000) + 1000;
        identifier = `${grade}${String(className).padStart(2, '0')}${newRandomNum}`;
        attempts++;
      }
    } else {
      // 确保identifier是字符串类型
      identifier = String(identifier).trim();
    }

    // 查找或创建学校（需要先获取school_id用于去重检查）
    const school = await findOrCreateSchool(schoolName.trim(), userRole === 'teacher' ? userId : null);
    if (!school) {
      return {
        success: false,
        error: '无法创建或找到学校'
      };
    }

    // 改进的去重检查：同时检查学号和姓名+学校+年级+班级组合
    const { data: existingStudents } = await db.supabase
      .from('students')
      .select('id, name, student_identifier')
      .or(`student_identifier.eq.${identifier},and(name.eq.${name.trim()},school_id.eq.${school.id},grade.eq.${parseInt(grade)},class.eq.${parseInt(className)})`);

    if (existingStudents && existingStudents.length > 0) {
      const existing = existingStudents[0];
      return {
        success: false,
        duplicate: true,
        student_identifier: existing.student_identifier,
        name: name.trim(),
        error: `学生已存在：${existing.name} (学号: ${existing.student_identifier})`
      };
    }



    // 准备学生数据
    const studentData = {
      student_identifier: identifier,
      name: name.trim(),
      grade: parseInt(grade),
      class: parseInt(className),
      school_id: school.id,
      status: 'active',
      created_at: new Date().toISOString()
    };

    // 添加可选字段
    if (groupNumber && !isNaN(parseInt(groupNumber))) {
      studentData.group_number = parseInt(groupNumber);
    }

    if (gender && gender.trim()) {
      studentData.gender = gender.trim();
    }

    if (seatNumber && !isNaN(parseInt(seatNumber))) {
      studentData.seat_number = parseInt(seatNumber);
    }

    // 创建学生
    const { data: newStudent, error: studentError } = await db.supabase
      .from('students')
      .insert(studentData)
      .select()
      .single();

    if (studentError) {
      return {
        success: false,
        error: '创建学生失败: ' + studentError.message
      };
    }

    // 为教师创建班级权限（如果是教师且没有该班级权限）
    let createdPermission = null;
    if (userRole === 'teacher' && !skipPermissionCreation) {
      createdPermission = await createTeacherPermissionIfNeeded(userId, school.id, grade, className);
    }

    return {
      success: true,
      student: newStudent,
      created_school: school.is_new ? school : null,
      created_permission: createdPermission
    };

  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 查找或创建学校（包含教师关联）
 */
async function findOrCreateSchool(schoolName, teacherId) {
  try {
    // 先查找是否存在
    const { data: existingSchool, error: findError } = await db.supabase
      .from('schools')
      .select('*')
      .eq('name', schoolName.trim())
      .single();

    if (existingSchool && !findError) {
      // 学校存在，检查教师是否已关联
      if (teacherId) {
        await ensureTeacherSchoolAssignment(teacherId, existingSchool.id);
      }
      return existingSchool;
    }

    // 不存在则创建
    const { data: newSchool, error: createError } = await db.supabase
      .from('schools')
      .insert({
        name: schoolName.trim(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error('创建学校失败:', createError);
      return null;
    }

    // 创建教师-学校关联
    if (teacherId) {
      await ensureTeacherSchoolAssignment(teacherId, newSchool.id);
    }

    return { ...newSchool, is_new: true };
  } catch (error) {
    console.error('查找或创建学校失败:', error);
    return null;
  }
}

/**
 * 确保教师-学校关联存在
 */
async function ensureTeacherSchoolAssignment(teacherId, schoolId) {
  try {
    // 检查关联是否已存在
    const { data: existingAssignment } = await db.supabase
      .from('teacher_school_assignments')
      .select('id')
      .eq('teacher_id', teacherId)
      .eq('school_id', schoolId)
      .single();

    if (existingAssignment) {
      return existingAssignment;
    }

    // 创建新的关联
    const { data: newAssignment, error: assignmentError } = await db.supabase
      .from('teacher_school_assignments')
      .insert({
        teacher_id: teacherId,
        school_id: schoolId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (assignmentError) {
      console.error('创建教师-学校关联失败:', assignmentError);
      return null;
    }

    console.log(`为教师 ${teacherId} 创建学校 ${schoolId} 的关联`);
    return newAssignment;
  } catch (error) {
    console.error('确保教师-学校关联失败:', error);
    return null;
  }
}

/**
 * 为教师创建班级权限（如果需要）
 */
async function createTeacherPermissionIfNeeded(teacherId, schoolId, grade, className) {
  try {
    // 检查是否已有权限
    const { data: existingPermission } = await db.supabase
      .from('teacher_class_permissions')
      .select('*')
      .eq('teacher_id', teacherId)
      .eq('school_id', schoolId)
      .eq('grade', grade)
      .eq('class', className)
      .single();

    if (existingPermission) {
      return null; // 已有权限
    }

    // 确保教师-学校关联存在（这是外键约束的前提）
    const assignment = await ensureTeacherSchoolAssignment(teacherId, schoolId);
    if (!assignment) {
      console.error(`无法创建教师-学校关联: teacherId=${teacherId}, schoolId=${schoolId}`);
      return null;
    }

    // 创建新权限
    const { data: newPermission, error } = await db.supabase
      .from('teacher_class_permissions')
      .insert({
        teacher_id: teacherId,
        school_id: schoolId,
        grade: parseInt(grade),
        class: parseInt(className),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('创建教师权限失败:', error);
      return null;
    }

    return { ...newPermission, is_new: true };
  } catch (error) {
    console.error('创建教师权限失败:', error);
    return null;
  }
}

/**
 * 检查教师是否有权限操作该学生
 */
async function checkTeacherStudentPermission(teacherId, student) {
  try {
    const { data: permissions, error } = await db.supabase
      .from('teacher_class_permissions')
      .select('*')
      .eq('teacher_id', teacherId)
      .eq('school_id', student.school_id)
      .eq('grade', student.grade)
      .eq('class', student.class);

    return !error && permissions && permissions.length > 0;
  } catch (error) {
    console.error('检查教师权限失败:', error);
    return false;
  }
}

/**
 * 获取教师任教的班级列表
 */
exports.getTeacherClasses = async (req, res) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    console.log(`用户 ${req.user.username} 请求任教班级列表`);

    if (userRole === 'admin') {
      // 管理员可以查看所有班级
      const { data: allClasses, error } = await db.supabase
        .from('students')
        .select('grade, class')
        .order('grade, class');

      if (error) throw error;

      // 去重
      const uniqueClasses = allClasses.reduce((acc, curr) => {
        const key = `${curr.grade}-${curr.class}`;
        if (!acc.find(item => `${item.grade}-${item.class}` === key)) {
          acc.push(curr);
        }
        return acc;
      }, []);

      res.json({
        success: true,
        data: uniqueClasses,
        total: uniqueClasses.length,
        is_admin: true
      });
    } else {
      // 普通教师只能查看自己任教的班级
      const { data: teacherClasses, error } = await db.supabase
        .from('teacher_classes')
        .select('grade, class')
        .eq('teacher_id', userId)
        .order('grade, class');

      if (error) throw error;

      res.json({
        success: true,
        data: teacherClasses,
        total: teacherClasses.length,
        is_admin: false
      });
    }

  } catch (error) {
    console.error('获取任教班级错误:', error);
    res.status(500).json({
      success: false,
      error: '获取任教班级失败: ' + error.message
    });
  }
};

/**
 * 管理员分配教师到班级
 */
exports.assignTeacherToClass = async (req, res) => {
  try {
    // 只有管理员可以执行此操作
    if (req.user?.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: '只有管理员可以分配教师到班级'
      });
    }

    const { teacher_id, grade, class: classNum } = req.body;

    if (!teacher_id || !grade || !classNum) {
      return res.status(400).json({
        success: false,
        error: '教师ID、年级和班级都是必需的'
      });
    }

    // 检查教师是否存在
    const { data: teacher, error: teacherError } = await db.supabase
      .from('users')
      .select('username, role')
      .eq('id', teacher_id)
      .single();

    if (teacherError || !teacher) {
      return res.status(404).json({
        success: false,
        error: '教师不存在'
      });
    }

    // 分配教师到班级
    const { data: assignment, error } = await db.supabase
      .from('teacher_classes')
      .insert({
        teacher_id,
        grade: parseInt(grade),
        class: parseInt(classNum)
      })
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // 唯一约束违反
        return res.status(409).json({
          success: false,
          error: '该教师已经被分配到此班级'
        });
      }
      throw error;
    }

    res.json({
      success: true,
      message: `成功将教师 ${teacher.username} 分配到 ${grade}年级${classNum}班`,
      data: assignment
    });

  } catch (error) {
    console.error('分配教师到班级错误:', error);
    res.status(500).json({
      success: false,
      error: '分配教师到班级失败: ' + error.message
    });
  }
};

// 导出multer中间件
exports.uploadExcel = upload.single('excel');

// 为了兼容性，创建addStudent别名
exports.addStudent = exports.createStudent;

// 导出所有函数
module.exports = {
  getAllStudents: exports.getAllStudents,
  addStudent: exports.addStudent,
  createStudent: exports.createStudent,
  getStudent: exports.getStudent,
  updateStudent: exports.updateStudent,
  deleteStudent: exports.deleteStudent,
  batchImportStudents: exports.batchImportStudents,
  uploadExcel: exports.uploadExcel,
  getTeacherClasses: exports.getTeacherClasses,
  assignTeacherToClass: exports.assignTeacherToClass
};