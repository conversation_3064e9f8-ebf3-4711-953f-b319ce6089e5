/**
 * 打字记录控制器
 * 处理所有与打字记录相关的请求
 */

const db = require('../config/db');

/**
 * 获取打字记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getTypingRecords = async (req, res) => {
  try {
    const { student_identifier } = req.query;

    if (!student_identifier) {
      return res.status(400).json({ error: '学生标识符为必填项' });
    }

    // 检查学生是否存在
    const { data: student, error: studentError } = await db.supabase
      .from('students')
      .select('*')
      .eq('student_identifier', student_identifier)
      .single();

    if (studentError || !student) {
      return res.status(404).json({ error: '学生不存在' });
    }

    // 获取打字记录
    const { data: records, error: recordsError } = await db.supabase
      .from('typing_records')
      .select('*')
      .eq('student_identifier', student_identifier)
      .order('created_at', { ascending: false });

    if (recordsError) {
      throw recordsError;
    }

    res.status(200).json({
      success: true,
      records: records || [],
      count: records ? records.length : 0
    });
  } catch (error) {
    console.error('获取打字记录错误:', error);
    res.status(500).json({ error: '获取打字记录失败: ' + error.message });
  }
};

/**
 * 获取最佳打字记录（支持教师权限控制）
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.getBestTypingRecords = async (req, res) => {
  try {
    const { grade, class: className, search, school_id, school } = req.query;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: '用户未认证' });
    }

    console.log(`用户 ${req.user.username} (${userRole}) 请求打字数据`);

    // 构建基础查询
    let query = db.supabase
      .from('students')
      .select(`
        *,
        medals!left(count),
        schools!left(name),
        typing_records!left(speed, accuracy)
      `);

    // 教师权限控制：只能查看自己任教班级的学生
    if (userRole !== 'admin') {
      // 获取教师有权限的班级
      const { data: permissions, error: permError } = await db.supabase
        .from('teacher_class_permissions')
        .select('school_id, grade, class')
        .eq('teacher_id', userId);

      if (permError) {
        console.error('获取教师权限失败:', permError);
        return res.status(500).json({ error: '获取权限信息失败' });
      }

      if (!permissions || permissions.length === 0) {
        console.log(`教师 ${req.user.username} 没有任何班级权限`);
        return res.status(200).json({ data: [] });
      }

      console.log(`教师 ${req.user.username} 的权限:`, permissions);

      // 构建权限过滤条件 - 使用 OR 条件组合
      const orConditions = permissions.map(p =>
        `and(school_id.eq.${p.school_id},grade.eq.${p.grade},class.eq.${p.class})`
      ).join(',');

      query = query.or(orConditions);
    } else {
      console.log(`管理员 ${req.user.username} 请求所有学生打字数据`);
    }

    // 添加筛选条件
    if (school_id) {
      query = query.eq('school_id', school_id);
    }

    if (school) {
      // 通过学校名称筛选，需要先查询学校ID
      const { data: schoolData, error: schoolError } = await db.supabase
        .from('schools')
        .select('id')
        .eq('name', school)
        .single();

      if (!schoolError && schoolData) {
        query = query.eq('school_id', schoolData.id);
      } else {
        // 如果找不到学校，返回空结果
        return res.status(200).json({ data: [] });
      }
    }

    if (grade) {
      query = query.eq('grade', grade);
    }

    if (className) {
      query = query.eq('class', className);
    }

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    // 执行查询并排序
    query = query.order('grade').order('class').order('name');

    const { data: students, error } = await query;

    if (error) {
      console.error('获取打字数据失败:', error);
      return res.status(500).json({ error: '获取打字数据失败: ' + error.message });
    }

    // 处理数据，计算最佳打字记录
    const processedStudents = students.map(student => {
      // 计算最佳打字速度和准确率
      let best_speed = 0;
      let best_accuracy = 0;

      if (student.typing_records && student.typing_records.length > 0) {
        best_speed = Math.max(...student.typing_records.map(r => r.speed || 0));
        best_accuracy = Math.max(...student.typing_records.map(r => r.accuracy || 0));
      }

      return {
        ...student,
        medal_count: student.medals?.[0]?.count || 0,
        school_name: student.schools?.name || '未知学校',
        best_speed,
        best_accuracy
      };
    });

    console.log(`查询到${processedStudents.length}条学生记录`);

    res.status(200).json({ data: processedStudents });
  } catch (error) {
    console.error('获取最佳打字记录错误:', error);
    res.status(500).json({ error: '获取最佳打字记录失败: ' + error.message });
  }
};

/**
 * 添加打字记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.addTypingRecord = async (req, res) => {
  try {
    const { student_identifier, speed, accuracy, date } = req.body;

    // 输出调试信息
    console.log('收到添加打字记录请求:', {
      student_identifier,
      speed,
      accuracy,
      date
    });

    // 验证必填字段
    if (!student_identifier || !speed) {
      return res.status(400).json({ error: '学生标识符和打字速度为必填项' });
    }

    // 检查学生是否存在
    console.log('查询学生:', student_identifier);
    const { data: student, error: studentError } = await db.supabase
      .from('students')
      .select('*')
      .eq('student_identifier', student_identifier)
      .single();

    console.log('查询结果:', student);

    if (studentError || !student) {
      console.log('学生不存在，尝试查询转换格式后的学生标识符');
      
      // 尝试分析学生标识符格式 (格式: grade_class_name)
      const parts = student_identifier.split('_');
      if (parts.length === 3) {
        const [grade, className, name] = parts;
        
        // 尝试以不同格式查询，例如 "一年级_1班_张三" 或 "1_1班_张三"
        const possibleIdentifiers = [
          student_identifier,
          `${grade}_${className}_${name}`,
        ];
        
        // 支持中文数字转换，如 "一年级" 和 "1年级"
        const gradeMap = {
          '1': '一年级', '2': '二年级', '3': '三年级',
          '4': '四年级', '5': '五年级', '6': '六年级',
          '一': '一年级', '二': '二年级', '三': '三年级',
          '四': '四年级', '五': '五年级', '六': '六年级'
        };
        
        // 尝试不同年级格式
        if (!isNaN(parseInt(grade))) {
          // 如果是数字格式
          if (gradeMap[grade]) {
            possibleIdentifiers.push(`${gradeMap[grade]}_${className}_${name}`);
          }
        } else if (grade.endsWith('年级')) {
          // 已经是 "X年级" 格式
          const numericGrade = grade.charAt(0);
          if (['一', '二', '三', '四', '五', '六'].includes(numericGrade)) {
            const gradeNumber = ['一', '二', '三', '四', '五', '六'].indexOf(numericGrade) + 1;
            possibleIdentifiers.push(`${gradeNumber}_${className}_${name}`);
          }
        }
        
        console.log('尝试以下可能的学生标识符:', possibleIdentifiers);
        
        // 查询所有可能的标识符
        const { data: studentQuery, error: queryError } = await db.supabase
          .from('students')
          .select('*')
          .in('student_identifier', possibleIdentifiers);

        console.log('扩展查询结果:', studentQuery);

        if (!queryError && studentQuery && studentQuery.length > 0) {
          console.log('找到学生:', studentQuery[0]);
          // 使用找到的第一个学生
          student = studentQuery[0];
        } else {
          return res.status(404).json({
            error: '学生不存在',
            details: {
              attempted_ids: possibleIdentifiers,
              message: '尝试了多种格式但未找到匹配的学生'
            }
          });
        }
      } else {
        return res.status(404).json({ 
          error: '学生不存在',
          details: {
            message: '无效的学生标识符格式，应为 "年级_班级_姓名"'
          }
        });
      }
    }
    
    // 格式化数据
    const typingDate = date ? new Date(date).toISOString() : new Date().toISOString();
    const typingAccuracy = accuracy || 100; // 默认准确率为100%

    console.log('准备插入打字记录:', {
      student_identifier: student.student_identifier,
      speed,
      typingAccuracy,
      typingDate
    });

    // 插入打字记录
    const { data: newRecord, error: insertError } = await db.supabase
      .from('typing_records')
      .insert({
        student_identifier: student.student_identifier,
        speed: speed,
        accuracy: typingAccuracy,
        created_at: typingDate
      })
      .select()
      .single();

    if (insertError) {
      console.error('插入打字记录失败:', insertError);
      return res.status(500).json({ error: '添加打字记录失败' });
    }

    console.log('打字记录添加成功');

    // 获取学生最佳记录
    const { data: bestRecords, error: bestError } = await db.supabase
      .from('typing_records')
      .select('speed, accuracy')
      .eq('student_identifier', student.student_identifier);

    if (bestError) {
      console.error('获取最佳记录失败:', bestError);
      return res.status(500).json({ error: '获取最佳记录失败' });
    }

    const best_speed = bestRecords.length > 0 ? Math.max(...bestRecords.map(r => r.speed)) : 0;
    const best_accuracy = bestRecords.length > 0 ? Math.max(...bestRecords.map(r => r.accuracy)) : 0;

    const result = {
      record: newRecord,
      best: {
        best_speed,
        best_accuracy
      }
    };

    res.status(201).json({ data: result });
  } catch (error) {
    console.error('添加打字记录错误:', error);
    res.status(500).json({ 
      error: '添加打字记录失败: ' + error.message,
      details: {
        request_body: req.body,
        stack: error.stack
      }
    });
  }
};

/**
 * 批量更新打字记录
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 */
exports.batchUpdateTypingSpeed = async (req, res) => {
  try {
    const { student_identifiers, speed } = req.body;

    // 验证必填字段
    if (!student_identifiers || !Array.isArray(student_identifiers) || student_identifiers.length === 0) {
      return res.status(400).json({ error: '学生标识符数组为必填项' });
    }

    if (!speed || isNaN(parseInt(speed))) {
      return res.status(400).json({ error: '打字速度必须是有效数字' });
    }

    const now = new Date().toISOString();
    const records = [];

    // 为每个学生创建打字记录
    for (const studentId of student_identifiers) {
      records.push({
        student_identifier: studentId,
        speed: parseInt(speed),
        accuracy: 100,
        created_at: now
      });
    }

    // 批量插入打字记录
    const { error: insertError } = await db.supabase
      .from('typing_records')
      .insert(records);

    if (insertError) {
      console.error('批量插入打字记录失败:', insertError);
      return res.status(500).json({ error: '批量更新打字速度失败' });
    }

    // 获取更新后的学生数据
    const { data: updatedStudents, error: queryError } = await db.supabase
      .from('students')
      .select(`
        *,
        medals!left(count),
        schools!left(name),
        typing_records!left(speed, accuracy)
      `)
      .in('student_identifier', student_identifiers);

    if (queryError) {
      console.error('获取更新后学生数据失败:', queryError);
      return res.status(500).json({ error: '获取更新后数据失败' });
    }

    // 处理数据，计算最佳打字记录
    const processedStudents = updatedStudents.map(student => {
      let best_speed = 0;
      let best_accuracy = 0;

      if (student.typing_records && student.typing_records.length > 0) {
        best_speed = Math.max(...student.typing_records.map(r => r.speed || 0));
        best_accuracy = Math.max(...student.typing_records.map(r => r.accuracy || 0));
      }

      return {
        ...student,
        medal_count: student.medals?.[0]?.count || 0,
        school_name: student.schools?.[0]?.name || '',
        best_speed,
        best_accuracy
      };
    });

    res.status(200).json({
      data: {
        message: `已为${student_identifiers.length}个学生更新打字速度`,
        students: processedStudents
      }
    });
  } catch (error) {
    console.error('批量更新打字速度错误:', error);
    res.status(500).json({ error: '批量更新打字速度失败: ' + error.message });
  }
};