/**
 * 管理员界面管理脚本 - 统一UI版本
 * 处理管理员控制台的所有功能，与数据库实时同步
 */

// 全局变量
let currentUser = null;
let currentSection = 'dashboard';
let allTeachers = [];
let allSchools = [];
let allStudents = [];
let allClasses = [];

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('管理员界面加载完成');

    // 检查必要的依赖
    if (typeof Utils === 'undefined') {
        console.error('Utils未加载，尝试创建基本实现');
        createBasicUtils();
    }

    if (typeof CONFIG === 'undefined') {
        console.error('CONFIG未加载，尝试创建基本配置');
        createBasicConfig();
    }

    initAdminManagement();
});

/**
 * 创建基本的Utils实现
 */
function createBasicUtils() {
    window.Utils = {
        showMessage: function(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);

            // 创建更美观的消息提示
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#4a90e2'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: 500;
                max-width: 300px;
                word-wrap: break-word;
            `;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        },
        getData: function(key, defaultValue = null) {
            try {
                const data = localStorage.getItem(key);
                return data ? JSON.parse(data) : defaultValue;
            } catch (error) {
                console.error('获取数据失败:', error);
                return defaultValue;
            }
        },
        setData: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('保存数据失败:', error);
                return false;
            }
        }
    };
}

/**
 * 创建基本的CONFIG配置
 */
function createBasicConfig() {
    window.CONFIG = {
        STORAGE: {
            USER: 'user',
            STUDENTS: 'students',
            TEACHERS: 'teachers',
            SCHOOLS: 'schools',
            CLASSES: 'classes'
        }
    };
}

/**
 * 初始化管理员管理界面
 */
async function initAdminManagement() {
    try {
        // 验证管理员权限
        await validateAdminAccess();
        
        // 绑定事件
        bindEvents();
        
        // 显示默认页面
        showAdminSection('dashboard');
        
    } catch (error) {
        console.error('初始化管理员界面失败:', error);
        Utils.showMessage('初始化失败，请重新登录');
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
    }
}

/**
 * 验证管理员访问权限
 */
async function validateAdminAccess() {
    try {
        // 从localStorage获取用户信息
        let user = Utils.getData(CONFIG.STORAGE.USER);

        // 如果没有用户信息，创建一个默认的管理员用户用于演示
        if (!user) {
            console.log('未找到用户信息，创建默认管理员用户');
            user = {
                id: 1,
                username: 'admin',
                display_name: '系统管理员',
                role: 'admin'
            };
            Utils.setData(CONFIG.STORAGE.USER, user);
        }

        if (user.role !== 'admin') {
            Utils.showMessage('您没有管理员权限');
            window.location.href = '/';
            return;
        }

        currentUser = user;
        console.log('当前用户:', currentUser);

        // 更新用户信息显示
        updateUserInfo();

        // 加载基础数据
        await loadBaseData();

    } catch (error) {
        console.error('权限验证失败:', error);
        throw error;
    }
}

/**
 * 更新用户信息显示
 */
function updateUserInfo() {
    const userInfo = document.getElementById('admin-user-info');
    if (userInfo && currentUser) {
        userInfo.textContent = `欢迎，${currentUser.display_name || currentUser.username}`;
    }
}

/**
 * 加载基础数据 - 从Supabase获取真实数据
 */
async function loadBaseData() {
    try {
        console.log('开始从Supabase加载基础数据...');

        // 检查DB是否可用
        if (typeof DB === 'undefined') {
            console.error('DB模块未加载，使用本地数据');
            loadLocalData();
            return;
        }

        // 并行加载所有数据
        const [studentsResult, teachersResult, schoolsResult, classesResult] = await Promise.allSettled([
            loadStudentsFromAPI(),
            loadTeachersFromAPI(),
            loadSchoolsFromAPI(),
            loadClassesFromAPI()
        ]);

        // 处理学生数据
        if (studentsResult.status === 'fulfilled') {
            allStudents = studentsResult.value || [];
            console.log('学生数据加载成功:', allStudents.length, '条');
        } else {
            console.error('学生数据加载失败:', studentsResult.reason);
            allStudents = Utils.getData(CONFIG.STORAGE.STUDENTS, []);
        }

        // 处理教师数据
        if (teachersResult.status === 'fulfilled') {
            allTeachers = teachersResult.value || [];
            console.log('教师数据加载成功:', allTeachers.length, '条');
        } else {
            console.error('教师数据加载失败:', teachersResult.reason);
            allTeachers = getDefaultTeachers();
        }

        // 处理学校数据
        if (schoolsResult.status === 'fulfilled') {
            allSchools = schoolsResult.value || [];
            console.log('学校数据加载成功:', allSchools.length, '条');
        } else {
            console.error('学校数据加载失败:', schoolsResult.reason);
            allSchools = getDefaultSchools();
        }

        // 处理班级数据
        if (classesResult.status === 'fulfilled') {
            allClasses = classesResult.value || [];
            console.log('班级数据加载成功:', allClasses.length, '条');
        } else {
            console.error('班级数据加载失败:', classesResult.reason);
            allClasses = getDefaultClasses();
        }

        console.log('基础数据加载完成');

    } catch (error) {
        console.error('加载基础数据失败:', error);
        Utils.showMessage('数据加载失败，使用本地数据: ' + error.message, 'error');
        loadLocalData();
    }
}

/**
 * 从API加载学生数据
 */
async function loadStudentsFromAPI() {
    try {
        // 直接调用学生API端点
        const response = await fetch('/api/admin/students', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('学生API响应:', result);

        return result.data || result || [];
    } catch (error) {
        console.error('API加载学生数据失败:', error);

        // 尝试使用备用方法
        try {
            const fallbackResponse = await fetch('/api/test-db', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ table: 'students' })
            });

            if (fallbackResponse.ok) {
                const fallbackResult = await fallbackResponse.json();
                return fallbackResult.data || [];
            }
        } catch (fallbackError) {
            console.error('备用方法也失败:', fallbackError);
        }

        throw error;
    }
}

/**
 * 从API加载教师数据
 */
async function loadTeachersFromAPI() {
    try {
        const response = await fetch('/api/admin/teachers', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('教师API响应:', result);

        return result.data || result || [];
    } catch (error) {
        console.error('API加载教师数据失败:', error);
        throw error;
    }
}

/**
 * 从API加载学校数据
 */
async function loadSchoolsFromAPI() {
    try {
        const response = await fetch('/api/admin/schools', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('学校API响应:', result);

        return result.data || result || [];
    } catch (error) {
        console.error('API加载学校数据失败:', error);
        // 如果API失败，返回默认数据
        return getDefaultSchools();
    }
}

/**
 * 从API加载班级数据
 */
async function loadClassesFromAPI() {
    try {
        const response = await fetch('/api/admin/classes', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('班级API响应:', result);

        return result.data || result || [];
    } catch (error) {
        console.error('API加载班级数据失败:', error);
        // 如果API失败，返回默认数据
        return getDefaultClasses();
    }
}

/**
 * 加载本地数据作为后备方案
 */
function loadLocalData() {
    allStudents = Utils.getData(CONFIG.STORAGE.STUDENTS, []);
    allTeachers = getDefaultTeachers();
    allSchools = getDefaultSchools();
    allClasses = getDefaultClasses();
    console.log('使用本地数据作为后备方案');
}

/**
 * 获取默认教师数据
 */
function getDefaultTeachers() {
    return [
        { id: 1, username: 'teacher1', display_name: '张老师', role: 'teacher', created_at: new Date().toISOString() },
        { id: 2, username: 'teacher2', display_name: '李老师', role: 'teacher', created_at: new Date().toISOString() },
        { id: 3, username: 'teacher3', display_name: '王老师', role: 'teacher', created_at: new Date().toISOString() }
    ];
}

/**
 * 获取默认学校数据
 */
function getDefaultSchools() {
    return [
        { id: 1, name: '示例小学', address: '北京市朝阳区示例街道123号', description: '这是一所示例小学' },
        { id: 2, name: '实验中学', address: '上海市浦东新区实验路456号', description: '这是一所实验中学' },
        { id: 3, name: '希望学校', address: '广州市天河区希望大道789号', description: '这是一所希望学校' }
    ];
}

/**
 * 获取默认班级数据
 */
function getDefaultClasses() {
    return [
        { id: 1, grade: '一年级', class: '1班', teacher_id: 1, school_id: 1 },
        { id: 2, grade: '一年级', class: '2班', teacher_id: 2, school_id: 1 },
        { id: 3, grade: '二年级', class: '1班', teacher_id: 3, school_id: 2 },
        { id: 4, grade: '二年级', class: '2班', teacher_id: 1, school_id: 2 }
    ];
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 退出登录事件
    const logoutBtn = document.querySelector('[onclick="adminLogout()"]');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', adminLogout);
    }
}

/**
 * 显示管理员界面的不同部分
 */
function showAdminSection(section) {
    console.log('显示管理员界面部分:', section);
    
    // 更新导航状态
    updateNavigation(section);
    
    // 显示对应内容
    const contentArea = document.getElementById('admin-content-area');
    if (!contentArea) {
        console.error('找不到内容区域');
        return;
    }
    
    currentSection = section;
    
    switch (section) {
        case 'dashboard':
            showDashboard();
            break;
        case 'teachers':
            showTeachersManagement();
            break;
        case 'schools':
            showSchoolsReadOnlyView();
            break;
        case 'students':
            showStudentsReadOnlyView();
            break;
        case 'classes':
            showClassesManagement();
            break;
        case 'assignments':
            showAssignmentsManagement();
            break;
        default:
            showDashboard();
    }
}

/**
 * 更新导航状态
 */
function updateNavigation(activeSection) {
    // 移除所有active类
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // 添加active类到当前选中的链接
    const activeLink = document.querySelector(`[onclick="showAdminSection('${activeSection}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

/**
 * 显示控制台概览
 */
function showDashboard() {
    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="dashboard-header" style="margin-bottom: 30px;">
            <h2 style="color: #333; margin-bottom: 10px;">
                <i class="fas fa-tachometer-alt"></i> 控制台概览
            </h2>
            <p style="color: #666;">欢迎使用班级成绩管理系统管理员控制台</p>
        </div>
        
        <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div class="stats-card">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3>${allStudents.length}</h3>
                        <p style="margin: 0; opacity: 0.9;">学生总数</p>
                    </div>
                    <i class="fas fa-users fa-2x" style="opacity: 0.7;"></i>
                </div>
            </div>
            
            <div class="stats-card">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3>${allTeachers.length}</h3>
                        <p style="margin: 0; opacity: 0.9;">教师总数</p>
                    </div>
                    <i class="fas fa-chalkboard-teacher fa-2x" style="opacity: 0.7;"></i>
                </div>
            </div>
            
            <div class="stats-card">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3>${allSchools.length}</h3>
                        <p style="margin: 0; opacity: 0.9;">学校总数</p>
                    </div>
                    <i class="fas fa-school fa-2x" style="opacity: 0.7;"></i>
                </div>
            </div>
            
            <div class="stats-card">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3>${allClasses.length}</h3>
                        <p style="margin: 0; opacity: 0.9;">班级总数</p>
                    </div>
                    <i class="fas fa-chalkboard fa-2x" style="opacity: 0.7;"></i>
                </div>
            </div>
        </div>
        
        <div class="quick-actions" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 20px; color: #333;">
                <i class="fas fa-bolt"></i> 快速操作
            </h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <button class="quick-action-btn" onclick="showAdminSection('teachers')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-plus"></i> 管理教师
                </button>
                <button class="quick-action-btn" onclick="showAdminSection('students')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-users"></i> 管理学生
                </button>
                <button class="quick-action-btn" onclick="showAdminSection('schools')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-school"></i> 管理学校
                </button>
                <button class="quick-action-btn" onclick="showAdminSection('classes')" style="padding: 15px; border: none; border-radius: 10px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; cursor: pointer; transition: transform 0.3s ease;">
                    <i class="fas fa-chalkboard"></i> 管理班级
                </button>
            </div>
        </div>
    `;
    
    // 添加按钮悬停效果
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * 显示教师管理
 */
async function showTeachersManagement() {
    console.log('=== 显示教师管理页面 ===');

    // 确保所有必要的数据都已加载
    if (!allSchools || allSchools.length === 0) {
        console.log('重新加载学校数据...');
        allSchools = await loadSchoolsFromAPI();
    }

    if (!allStudents || allStudents.length === 0) {
        console.log('重新加载学生数据...');
        allStudents = await loadStudentsFromAPI();
    }

    if (!allTeachers || allTeachers.length === 0) {
        console.log('重新加载教师数据...');
        allTeachers = await loadTeachersFromAPI();
    }

    console.log('数据加载完成:');
    console.log('- 学校数量:', allSchools ? allSchools.length : 0);
    console.log('- 学生数量:', allStudents ? allStudents.length : 0);
    console.log('- 教师数量:', allTeachers ? allTeachers.length : 0);

    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-chalkboard-teacher"></i> 教师管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理系统中的所有教师账户</p>
            </div>
            <button onclick="showAddTeacherForm()" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加教师
            </button>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">用户名</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">显示名称</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">任教学校</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">所教年级</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">操作</th>
                    </tr>
                </thead>
                <tbody id="teachers-table-body">
                    ${renderTeachersTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 渲染教师表格
 */
function renderTeachersTable() {
    if (allTeachers.length === 0) {
        return `
            <tr>
                <td colspan="5" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-users fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无教师数据</p>
                    <p style="font-size: 14px; opacity: 0.7;">教师数据将从Supabase数据库加载</p>
                </td>
            </tr>
        `;
    }

    return allTeachers.map(teacher => {
        // 获取教师的任教信息
        const schools = teacher.teaching_schools || [];
        const assignments = teacher.assignment_summary || '暂无任教配置';

        // 生成学校徽章
        let schoolBadges = '';
        if (schools.length > 0) {
            schoolBadges = schools.map(school =>
                `<span class="badge bg-primary me-1 mb-1">${school}</span>`
            ).join('');
        } else {
            schoolBadges = '<span class="text-muted small">暂无</span>';
        }

        // 处理任教班级信息
        let assignmentDisplay = assignments;
        if (assignments === '暂无任教配置') {
            assignmentDisplay = '<span class="text-muted small">暂无任教配置</span>';
        } else if (assignments.length > 50) {
            // 如果信息太长，截断并添加省略号
            assignmentDisplay = `<span title="${assignments}">${assignments.substring(0, 50)}...</span>`;
        }

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">${teacher.username}</td>
                <td style="padding: 15px;">
                    <strong>${teacher.display_name || teacher.username}</strong>
                    ${teacher.display_name && teacher.display_name !== teacher.username ?
                        `<br><small class="text-muted">@${teacher.username}</small>` : ''}
                </td>
                <td style="padding: 15px;">
                    <div style="max-width: 200px;">
                        <small class="text-muted d-block mb-1">任教学校:</small>
                        ${schoolBadges}
                    </div>
                </td>
                <td style="padding: 15px;">
                    <div style="max-width: 300px; font-size: 13px; line-height: 1.4;">
                        <small class="text-muted d-block mb-1">任教班级:</small>
                        ${assignmentDisplay}
                    </div>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <button onclick="editTeacher('${teacher.id}')" class="btn btn-success mb-1" title="编辑教师信息和任教配置">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button onclick="viewTeacherPermissions('${teacher.id}')" class="btn btn-info mb-1" title="查看详细任教权限">
                            <i class="fas fa-eye"></i> 权限
                        </button>
                    </div>
                    <div class="btn-group-vertical btn-group-sm mt-1" role="group">
                        <button onclick="resetTeacherPassword('${teacher.id}', '${teacher.display_name || teacher.username}')" class="btn btn-warning mb-1" title="重置登录密码">
                            <i class="fas fa-key"></i> 重置
                        </button>
                        <button onclick="deleteTeacher('${teacher.id}', '${teacher.display_name || teacher.username}')" class="btn btn-danger" title="删除教师账户">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 显示学生信息只读视图
 */
async function showStudentsReadOnlyView() {
    console.log('=== 显示学生信息只读视图 ===');

    // 确保学生数据已加载
    if (!allStudents || allStudents.length === 0) {
        console.log('重新加载学生数据...');
        allStudents = await loadStudentsFromAPI();
    }

    // 确保学校数据已加载
    if (!allSchools || allSchools.length === 0) {
        console.log('重新加载学校数据...');
        allSchools = await loadSchoolsFromAPI();
    }

    console.log('数据加载完成:');
    console.log('- 学生数量:', allStudents ? allStudents.length : 0);
    console.log('- 学校数量:', allSchools ? allSchools.length : 0);

    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-users"></i> 学生信息
                    <span class="badge bg-secondary ms-2">只读模式</span>
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">查看系统中的学生信息（编辑功能已转移至教师管理界面）</p>
            </div>
            <div class="alert alert-info mb-0 py-2 px-3">
                <i class="fas fa-info-circle me-2"></i>
                学生管理功能已转移至教师管理界面
            </div>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学生姓名</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学校</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">年级班级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学号</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">状态</th>
                    </tr>
                </thead>
                <tbody id="students-table-body">
                    ${renderStudentsReadOnlyTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 显示学生管理（保留原函数）
 */
function showStudentsManagement() {
    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-users"></i> 学生管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理系统中的所有学生信息</p>
            </div>
            <button onclick="showAddStudentForm()" style="padding: 10px 20px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-plus"></i> 添加学生
            </button>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">姓名</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">年级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">班级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">座位号</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">性别</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">操作</th>
                    </tr>
                </thead>
                <tbody id="students-table-body">
                    ${renderStudentsTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 渲染学生只读表格
 */
function renderStudentsReadOnlyTable() {
    if (allStudents.length === 0) {
        return `
            <tr>
                <td colspan="5" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-users fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无学生数据</p>
                    <p style="font-size: 14px; opacity: 0.7;">学生数据将从数据库加载</p>
                </td>
            </tr>
        `;
    }

    return allStudents.map(student => {
        // 处理不同的数据格式
        const studentName = student.name || student.student_name || '未知';
        const studentGrade = student.grade || student.student_grade || '未知';
        const studentClass = student.class || student.student_class || '未知';
        const studentId = student.student_id || student.id || '未设置';
        const schoolId = student.school_id || student.school || '';

        // 查找学校名称
        const school = allSchools.find(s => s.id == schoolId);
        const schoolName = school ? school.name : '未知学校';

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user text-primary me-2"></i>
                        <strong>${studentName}</strong>
                    </div>
                </td>
                <td style="padding: 15px;">
                    <span class="badge bg-light text-dark">${schoolName}</span>
                </td>
                <td style="padding: 15px;">
                    ${studentGrade}年级${studentClass}班
                </td>
                <td style="padding: 15px;">
                    <code>${studentId}</code>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <span class="badge bg-secondary">
                        <i class="fas fa-eye me-1"></i>只读模式
                    </span>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 渲染学生表格（保留原函数）
 */
function renderStudentsTable() {
    if (allStudents.length === 0) {
        return `
            <tr>
                <td colspan="6" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-user-graduate fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无学生数据</p>
                    <p style="font-size: 14px; opacity: 0.7;">学生数据将从Supabase数据库加载</p>
                </td>
            </tr>
        `;
    }

    return allStudents.map(student => {
        // 处理不同的数据格式
        const studentName = student.name || student.student_name || '未知';
        const studentGrade = student.grade || student.student_grade || '未知';
        const studentClass = student.class || student.student_class || '未知';
        const studentGender = student.gender || student.student_gender || '未知';
        const studentId = student.id || student.student_id || '';
        const studentSeat = student.seat || student.student_seat || '';

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">${studentName}</td>
                <td style="padding: 15px;">${studentGrade}</td>
                <td style="padding: 15px;">${studentClass}</td>
                <td style="padding: 15px;">${studentSeat}</td>
                <td style="padding: 15px;">${studentGender}</td>
                <td style="padding: 15px; text-align: center;">
                    <button onclick="editStudent('${studentId}')" class="action-btn edit">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button onclick="deleteStudent('${studentId}')" class="action-btn delete">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 显示学校信息只读视图
 */
async function showSchoolsReadOnlyView() {
    console.log('=== 显示学校信息只读视图 ===');

    // 确保学校数据已加载
    if (!allSchools || allSchools.length === 0) {
        console.log('重新加载学校数据...');
        allSchools = await loadSchoolsFromAPI();
    }

    // 确保学生数据已加载（用于统计）
    if (!allStudents || allStudents.length === 0) {
        console.log('重新加载学生数据...');
        allStudents = await loadStudentsFromAPI();
    }

    console.log('数据加载完成:');
    console.log('- 学校数量:', allSchools ? allSchools.length : 0);
    console.log('- 学生数量:', allStudents ? allStudents.length : 0);

    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-school"></i> 学校信息
                    <span class="badge bg-secondary ms-2">只读模式</span>
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">查看系统中的学校信息（编辑功能已转移至教师管理界面）</p>
            </div>
            <div class="alert alert-info mb-0 py-2 px-3">
                <i class="fas fa-info-circle me-2"></i>
                学校管理功能已转移至教师管理界面
            </div>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学校名称</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">年级班级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学生数量</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">状态</th>
                    </tr>
                </thead>
                <tbody id="schools-table-body">
                    ${renderSchoolsReadOnlyTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 显示学校管理（保留原函数用于其他地方调用）
 */
async function showSchoolsManagement() {
    console.log('=== 显示学校管理页面 ===');

    // 确保学校数据已加载
    if (!allSchools || allSchools.length === 0) {
        console.log('重新加载学校数据...');
        allSchools = await loadSchoolsFromAPI();
    }

    // 确保学生数据已加载（用于统计）
    if (!allStudents || allStudents.length === 0) {
        console.log('重新加载学生数据...');
        allStudents = await loadStudentsFromAPI();
    }

    console.log('数据加载完成:');
    console.log('- 学校数量:', allSchools ? allSchools.length : 0);
    console.log('- 学生数量:', allStudents ? allStudents.length : 0);
    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-school"></i> 学校管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理系统中的所有学校信息</p>
            </div>
            <button onclick="showAddSchoolForm()" style="padding: 10px 20px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-plus"></i> 添加学校
            </button>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学校名称</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">年级班级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">学生数量</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">操作</th>
                    </tr>
                </thead>
                <tbody id="schools-table-body">
                    ${renderSchoolsTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 渲染学校只读表格
 */
function renderSchoolsReadOnlyTable() {
    if (allSchools.length === 0) {
        return `
            <tr>
                <td colspan="4" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-school fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无学校数据</p>
                    <p style="font-size: 14px; opacity: 0.7;">学校数据将从数据库加载</p>
                </td>
            </tr>
        `;
    }

    return allSchools.map(school => {
        // 统计该学校的年级班级和学生数量
        const schoolStats = getSchoolStats(school.id);

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-school text-primary me-2"></i>
                        <strong>${school.name}</strong>
                    </div>
                </td>
                <td style="padding: 15px;">
                    <div class="small">
                        ${schoolStats.gradeClassInfo || '<span class="text-muted">暂无数据</span>'}
                    </div>
                </td>
                <td style="padding: 15px;">
                    <span class="badge bg-info">${schoolStats.studentCount} 名学生</span>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <span class="badge bg-secondary">
                        <i class="fas fa-eye me-1"></i>只读模式
                    </span>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 渲染学校表格（保留原函数）
 */
function renderSchoolsTable() {
    if (allSchools.length === 0) {
        return `
            <tr>
                <td colspan="4" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-school fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无学校数据</p>
                </td>
            </tr>
        `;
    }

    return allSchools.map(school => {
        // 统计该学校的年级班级和学生数量
        const schoolStats = getSchoolStats(school.id);

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-school text-primary me-2"></i>
                        <strong>${school.name}</strong>
                    </div>
                </td>
                <td style="padding: 15px;">
                    <div class="small">
                        ${schoolStats.gradeClassInfo || '<span class="text-muted">暂无数据</span>'}
                    </div>
                </td>
                <td style="padding: 15px;">
                    <span class="badge bg-info">${schoolStats.studentCount} 名学生</span>
                </td>
                <td style="padding: 15px; text-align: center;">
                    <button onclick="manageSchoolGradesClasses('${school.id}', '${school.name}')" class="btn btn-sm btn-primary me-1">
                        <i class="fas fa-cog"></i> 年级班级
                    </button>
                    <button onclick="editSchool(${school.id})" class="btn btn-sm btn-success me-1">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button onclick="deleteSchool(${school.id})" class="btn btn-sm btn-danger">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 获取学校统计信息
 */
function getSchoolStats(schoolId) {
    if (!allStudents || allStudents.length === 0) {
        return {
            gradeClassInfo: '<span class="text-muted">暂无数据</span>',
            studentCount: 0
        };
    }

    // 统计该学校的学生
    const schoolStudents = allStudents.filter(student => {
        const studentSchoolId = String(student.school_id || student.school || '');
        return String(schoolId) === studentSchoolId;
    });

    if (schoolStudents.length === 0) {
        return {
            gradeClassInfo: '<span class="text-muted">暂无数据</span>',
            studentCount: 0
        };
    }

    // 统计年级和班级
    const gradeClassMap = new Map();

    schoolStudents.forEach(student => {
        const grade = student.grade || student.student_grade || student.class_grade;
        const className = student.class || student.student_class || student.class_name;

        if (grade && className) {
            const gradeKey = `${grade}年级`;
            if (!gradeClassMap.has(gradeKey)) {
                gradeClassMap.set(gradeKey, new Set());
            }
            gradeClassMap.get(gradeKey).add(className);
        }
    });

    // 生成显示文本
    let gradeClassInfo = '';
    if (gradeClassMap.size > 0) {
        const gradeInfos = [];
        for (const [grade, classes] of gradeClassMap) {
            const classCount = classes.size;
            gradeInfos.push(`${grade}(${classCount}班)`);
        }
        gradeClassInfo = gradeInfos.join(', ');
    } else {
        gradeClassInfo = '<span class="text-muted">暂无数据</span>';
    }

    return {
        gradeClassInfo,
        studentCount: schoolStudents.length
    };
}

/**
 * 管理学校年级班级
 */
function manageSchoolGradesClasses(schoolId, schoolName) {
    console.log('管理学校年级班级:', schoolId, schoolName);

    // 获取该学校的详细统计
    const schoolStats = getDetailedSchoolStats(schoolId);

    // 显示年级班级管理模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'schoolGradeClassModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-school text-primary me-2"></i>
                        ${schoolName} - 年级班级管理
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">
                                <i class="fas fa-chart-bar text-info me-2"></i>
                                当前统计
                            </h6>
                            <div class="card border-info">
                                <div class="card-body">
                                    ${generateSchoolStatsDisplay(schoolStats)}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">
                                <i class="fas fa-plus text-success me-2"></i>
                                添加年级班级
                            </h6>
                            <div class="card border-success">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">年级</label>
                                        <select class="form-control" id="newGrade">
                                            <option value="">选择年级</option>
                                            <option value="1">1年级</option>
                                            <option value="2">2年级</option>
                                            <option value="3">3年级</option>
                                            <option value="4">4年级</option>
                                            <option value="5">5年级</option>
                                            <option value="6">6年级</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">班级</label>
                                        <input type="number" class="form-control" id="newClass"
                                               placeholder="输入班级号" min="1" max="20">
                                    </div>
                                    <button onclick="addGradeClass('${schoolId}')" class="btn btn-success">
                                        <i class="fas fa-plus"></i> 添加
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 模态框关闭时移除DOM元素
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * 获取学校详细统计
 */
function getDetailedSchoolStats(schoolId) {
    if (!allStudents || allStudents.length === 0) {
        return { grades: new Map() };
    }

    const schoolStudents = allStudents.filter(student => {
        const studentSchoolId = String(student.school_id || student.school || '');
        return String(schoolId) === studentSchoolId;
    });

    const gradeClassMap = new Map();

    schoolStudents.forEach(student => {
        const grade = student.grade || student.student_grade || student.class_grade;
        const className = student.class || student.student_class || student.class_name;

        if (grade && className) {
            if (!gradeClassMap.has(grade)) {
                gradeClassMap.set(grade, new Map());
            }

            const classMap = gradeClassMap.get(grade);
            if (!classMap.has(className)) {
                classMap.set(className, 0);
            }
            classMap.set(className, classMap.get(className) + 1);
        }
    });

    return { grades: gradeClassMap };
}

/**
 * 生成学校统计显示
 */
function generateSchoolStatsDisplay(schoolStats) {
    if (schoolStats.grades.size === 0) {
        return '<p class="text-muted">暂无年级班级数据</p>';
    }

    let html = '';
    for (const [grade, classMap] of schoolStats.grades) {
        html += `
            <div class="mb-2">
                <strong>${grade}年级：</strong>
                <div class="ms-3">
        `;

        for (const [className, studentCount] of classMap) {
            html += `
                <span class="badge bg-light text-dark me-1 mb-1">
                    ${className}班 (${studentCount}人)
                </span>
            `;
        }

        html += '</div></div>';
    }

    return html;
}

/**
 * 添加年级班级
 */
async function addGradeClass(schoolId) {
    const grade = document.getElementById('newGrade').value;
    const className = document.getElementById('newClass').value;

    if (!grade || !className) {
        Utils.showMessage('请选择年级并输入班级号', 'error');
        return;
    }

    // 这里可以添加到数据库的逻辑
    // 目前先显示提示信息
    Utils.showMessage(`已添加 ${grade}年级${className}班 到学校配置中`, 'success');

    // 刷新学校管理页面
    showAdminSection('schools');

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('schoolGradeClassModal'));
    if (modal) {
        modal.hide();
    }
}

/**
 * 显示班级管理
 */
function showClassesManagement() {
    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-chalkboard"></i> 班级管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理系统中的所有班级信息</p>
            </div>
            <button onclick="showAddClassForm()" style="padding: 10px 20px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; border: none; border-radius: 8px; cursor: pointer;">
                <i class="fas fa-plus"></i> 添加班级
            </button>
        </div>

        <div class="table-container" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">年级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">班级</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">班主任</th>
                        <th style="padding: 15px; text-align: left; font-weight: 600; color: #495057;">所属学校</th>
                        <th style="padding: 15px; text-align: center; font-weight: 600; color: #495057;">操作</th>
                    </tr>
                </thead>
                <tbody id="classes-table-body">
                    ${renderClassesTable()}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 渲染班级表格
 */
function renderClassesTable() {
    if (allClasses.length === 0) {
        return `
            <tr>
                <td colspan="5" style="padding: 30px; text-align: center; color: #666;">
                    <i class="fas fa-chalkboard fa-2x" style="margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>暂无班级数据</p>
                </td>
            </tr>
        `;
    }

    return allClasses.map(classItem => {
        const teacher = allTeachers.find(t => t.id === classItem.teacher_id);
        const school = allSchools.find(s => s.id === classItem.school_id);

        return `
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 15px;">${classItem.grade}</td>
                <td style="padding: 15px;">${classItem.class}</td>
                <td style="padding: 15px;">${teacher ? teacher.display_name : '未分配'}</td>
                <td style="padding: 15px;">${school ? school.name : '未分配'}</td>
                <td style="padding: 15px; text-align: center;">
                    <button onclick="editClass(${classItem.id})" style="padding: 5px 10px; margin: 0 2px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button onclick="deleteClass(${classItem.id})" style="padding: 5px 10px; margin: 0 2px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 显示权限分配管理
 */
function showAssignmentsManagement() {
    const contentArea = document.getElementById('admin-content-area');
    contentArea.innerHTML = `
        <div class="management-header" style="margin-bottom: 30px;">
            <h2 style="color: #333; margin: 0;">
                <i class="fas fa-user-cog"></i> 权限分配
            </h2>
            <p style="color: #666; margin: 5px 0 0 0;">管理教师与班级、学校的关联关系</p>
        </div>

        <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <h3 style="color: #333; margin-bottom: 20px;"><i class="fas fa-user-shield"></i> 权限分配管理</h3>

            <div style="margin-bottom: 20px;">
                <h5>教师权限管理</h5>
                <div id="teacherPermissionsList">
                    ${allTeachers.map(teacher => `
                        <div style="display: flex; justify-content: between; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 10px;">
                            <div>
                                <strong>${teacher.displayName || teacher.username}</strong>
                                <small style="color: #666; display: block;">用户名: ${teacher.username}</small>
                            </div>
                            <div>
                                <button onclick="manageTeacherPermissions(${teacher.id})" style="padding: 5px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                    管理权限
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div style="text-align: center;">
                <button onclick="showAdminSection('dashboard')" style="padding: 10px 20px; background: linear-gradient(135deg, #4a90e2 0%, #67b8ff 100%); color: white; border: none; border-radius: 8px; cursor: pointer;">
                    返回控制台
                </button>
            </div>
        </div>
    `;
}

/**
 * 管理员退出登录
 */
function adminLogout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除用户数据
        localStorage.removeItem(CONFIG.STORAGE.USER);
        localStorage.removeItem('token');

        Utils.showMessage('已退出登录');

        // 跳转到首页
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);
    }
}

/**
 * 显示添加教师表单
 */
async function showAddTeacherForm() {
    // 加载学校选项
    await loadSchoolOptions('teacherSchools');

    const modal = new bootstrap.Modal(document.getElementById('addTeacherModal'));
    // 清空表单
    document.getElementById('addTeacherForm').reset();

    // 绑定年级和学校变化事件
    bindTeacherFormEvents();

    modal.show();
}

/**
 * 绑定教师表单事件
 */
function bindTeacherFormEvents() {
    console.log('绑定教师表单事件');

    const schoolSelect = document.getElementById('teacherSchools');
    const assignmentContainer = document.getElementById('teacherAssignmentContainer');

    // 检查数据加载状态
    console.log('当前数据状态:');
    console.log('- allSchools:', allSchools ? allSchools.length : 'null');
    console.log('- allStudents:', allStudents ? allStudents.length : 'null');

    // 页面加载时立即加载学校选项
    loadSchoolOptions('teacherSchools');

    // 当学校选择改变时，重新生成分层级配置界面
    schoolSelect.addEventListener('change', () => {
        console.log('学校选择改变');
        const selectedSchools = Array.from(schoolSelect.selectedOptions).map(option => ({
            id: option.value,
            name: option.text
        })).filter(school => school.id);

        console.log('选中的学校:', selectedSchools);

        if (selectedSchools.length > 0) {
            generateSchoolAssignmentInterface(selectedSchools, assignmentContainer);
        } else {
            // 显示默认提示
            assignmentContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                    <p>请先选择任教学校，然后为每个学校配置年级和班级</p>
                </div>
            `;
        }
    });
}

/**
 * 生成学校分层级任教配置界面
 */
async function generateSchoolAssignmentInterface(selectedSchools, container) {
    console.log('生成分层级配置界面，学校:', selectedSchools);

    container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';

    let html = '';

    for (const school of selectedSchools) {
        // 获取该学校的年级数据
        const schoolGrades = await getGradesForSchool(school.id);

        html += `
            <div class="school-assignment-card mb-4 border-0 rounded-3 shadow-sm"
                 style="background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%); border-left: 4px solid #4a90e2 !important;">

                <!-- 学校标题 -->
                <div class="card-header bg-transparent border-0 pb-2">
                    <h6 class="mb-0 d-flex align-items-center text-primary fw-bold">
                        <div class="me-3 p-2 rounded-circle" style="background: rgba(74, 144, 226, 0.1);">
                            <i class="fas fa-school"></i>
                        </div>
                        ${school.name}
                        <span class="badge bg-primary ms-auto">配置中</span>
                    </h6>
                </div>

                <div class="card-body pt-3">
                    <div class="row g-4">
                        <!-- 年级选择 -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-bold d-flex align-items-center mb-2">
                                    <i class="fas fa-layer-group text-success me-2"></i>
                                    所教年级
                                </label>
                                <div class="position-relative">
                                    <select multiple class="form-control school-grades border-2"
                                            data-school-id="${school.id}"
                                            style="height: 120px; border-color: #e3f2fd; border-radius: 10px;">
                                        ${schoolGrades.length > 0 ?
                                            schoolGrades.map(grade =>
                                                `<option value="${grade}" class="py-1">${grade}年级</option>`
                                            ).join('') :
                                            '<option value="" disabled>暂无年级数据</option>'
                                        }
                                    </select>
                                    <small class="text-muted d-flex align-items-center mt-1">
                                        <i class="fas fa-info-circle me-1"></i>
                                        按住Ctrl键可选择多个年级
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- 班级选择 -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label fw-bold d-flex align-items-center mb-2">
                                    <i class="fas fa-users text-warning me-2"></i>
                                    任课班级
                                </label>
                                <div class="position-relative">
                                    <select multiple class="form-control school-classes border-2"
                                            data-school-id="${school.id}"
                                            style="height: 120px; border-color: #fff3e0; border-radius: 10px;" disabled>
                                        <option value="" disabled>请先选择年级</option>
                                    </select>
                                    <small class="text-muted d-flex align-items-center mt-1">
                                        <i class="fas fa-info-circle me-1"></i>
                                        基于年级动态加载班级选项
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 配置状态指示 -->
                    <div class="mt-3 p-2 rounded" style="background: rgba(74, 144, 226, 0.05);">
                        <small class="text-muted d-flex align-items-center">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            提示：选择年级后将自动加载该学校对应年级的班级选项
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    container.innerHTML = html;

    // 绑定年级选择事件
    container.querySelectorAll('.school-grades').forEach(gradeSelect => {
        gradeSelect.addEventListener('change', async () => {
            const schoolId = gradeSelect.dataset.schoolId;
            const selectedGrades = Array.from(gradeSelect.selectedOptions).map(option => option.value);
            const classSelect = container.querySelector(`.school-classes[data-school-id="${schoolId}"]`);

            if (selectedGrades.length > 0) {
                const classes = await getClassesForSchoolAndGrades(schoolId, selectedGrades);
                classSelect.disabled = false;
                classSelect.innerHTML = classes.map(cls =>
                    `<option value="${cls}">${cls}班</option>`
                ).join('');
            } else {
                classSelect.disabled = true;
                classSelect.innerHTML = '<option value="">请先选择年级</option>';
            }
        });
    });
}

/**
 * 获取指定学校的年级列表
 */
async function getGradesForSchool(schoolId) {
    console.log('获取学校年级:', schoolId);

    // 确保学生数据已加载
    if (!allStudents || allStudents.length === 0) {
        allStudents = await loadStudentsFromAPI();
    }

    const grades = new Set();

    // 从学生数据中提取年级
    allStudents.forEach(student => {
        const studentGrade = student.grade || student.student_grade || student.class_grade;
        const studentSchoolId = String(student.school_id || student.school || '');

        if (String(schoolId) === studentSchoolId && studentGrade) {
            grades.add(parseInt(studentGrade));
        }
    });

    // 如果没有学生数据，返回空数组
    if (grades.size === 0) {
        console.log('该学校暂无学生数据');
        return [];
    }

    return Array.from(grades).sort((a, b) => a - b);
}

/**
 * 获取指定学校和年级的班级列表
 */
async function getClassesForSchoolAndGrades(schoolId, grades) {
    console.log('获取学校班级:', schoolId, grades);

    // 确保学生数据已加载
    if (!allStudents || allStudents.length === 0) {
        allStudents = await loadStudentsFromAPI();
    }

    const classes = new Set();

    // 从学生数据中提取班级
    allStudents.forEach(student => {
        const studentGrade = parseInt(student.grade || student.student_grade || student.class_grade);
        const studentClass = student.class || student.student_class || student.class_name;
        const studentSchoolId = String(student.school_id || student.school || '');

        if (String(schoolId) === studentSchoolId &&
            grades.includes(String(studentGrade)) &&
            studentClass) {
            classes.add(parseInt(studentClass));
        }
    });

    // 如果没有学生数据，返回空数组
    if (classes.size === 0) {
        console.log('该学校年级暂无学生数据');
        return [];
    }

    return Array.from(classes).sort((a, b) => a - b);
}

/**
 * 加载学校选项
 */
async function loadSchoolOptions(selectId) {
    console.log('开始加载学校选项，selectId:', selectId);
    const select = document.getElementById(selectId);

    if (!select) {
        console.error('找不到选择器元素:', selectId);
        return;
    }

    select.innerHTML = '<option value="">加载中...</option>';

    try {
        // 确保allSchools数组已加载
        if (!allSchools || allSchools.length === 0) {
            console.log('allSchools为空，尝试重新加载');
            allSchools = await loadSchoolsFromAPI();
        }

        console.log('=== 学校选项加载调试信息 ===');
        console.log('可用学校数据:', allSchools);

        if (allSchools && allSchools.length > 0) {
            console.log('学校数据样例:', allSchools.slice(0, 3));
            console.log('第一个学校的字段:', Object.keys(allSchools[0]));

            select.innerHTML = allSchools.map(school =>
                `<option value="${school.id}">${school.name}</option>`
            ).join('');
            console.log('学校选项加载成功，共', allSchools.length, '个学校');
        } else {
            select.innerHTML = '<option value="">暂无学校数据</option>';
            console.log('没有可用的学校数据');
        }
    } catch (error) {
        console.error('加载学校选项失败:', error);
        select.innerHTML = '<option value="">加载失败</option>';
    }
}

/**
 * 加载年级选项
 */
async function loadGradeOptions(schoolIds, selectId) {
    console.log('开始加载年级选项，schoolIds:', schoolIds, 'selectId:', selectId);
    const select = document.getElementById(selectId);

    if (!select) {
        console.error('找不到选择器元素:', selectId);
        return;
    }

    if (!schoolIds || schoolIds.length === 0) {
        select.innerHTML = '<option value="">请先选择学校</option>';
        select.disabled = true;
        return;
    }

    select.innerHTML = '<option value="">加载中...</option>';
    select.disabled = false;

    try {
        // 确保allStudents数组已加载
        if (!allStudents || allStudents.length === 0) {
            console.log('allStudents为空，尝试重新加载');
            allStudents = await loadStudentsFromAPI();
        }

        console.log('=== 年级选项加载调试信息 ===');
        console.log('可用学生数据总数:', allStudents.length);
        console.log('选中的学校ID:', schoolIds);

        // 调试：打印前几个学生数据的结构
        if (allStudents.length > 0) {
            console.log('学生数据样例:', allStudents.slice(0, 5));
            console.log('第一个学生的所有字段:', Object.keys(allStudents[0]));
        } else {
            console.log('警告：没有学生数据！');
            select.innerHTML = '<option value="">没有学生数据</option>';
            return;
        }

        // 基于学生数据生成年级选项
        const grades = new Set();

        allStudents.forEach(student => {
            // 尝试多种可能的字段名
            const studentGrade = student.grade || student.student_grade || student.class_grade;
            const studentSchoolId = String(student.school_id || student.school || '');

            console.log('处理学生:', {
                grade: studentGrade,
                schoolId: studentSchoolId,
                selectedSchools: schoolIds,
                student: student
            });

            // 检查学生是否属于所选学校（支持多种数据类型匹配）
            const schoolMatches = schoolIds.some(selectedId => {
                return String(selectedId) === studentSchoolId ||
                       parseInt(selectedId) === parseInt(studentSchoolId);
            });

            if (schoolMatches && studentGrade) {
                const gradeNum = parseInt(studentGrade);
                if (!isNaN(gradeNum)) {
                    grades.add(gradeNum);
                    console.log('添加年级:', gradeNum);
                }
            }
        });

        console.log('找到的年级:', Array.from(grades));

        if (grades.size > 0) {
            const sortedGrades = Array.from(grades).sort((a, b) => a - b);
            select.innerHTML = sortedGrades.map(grade =>
                `<option value="${grade}">${grade}年级</option>`
            ).join('');
            console.log('年级选项加载成功，共', grades.size, '个年级');
        } else {
            select.innerHTML = '<option value="">暂无年级数据</option>';
            console.log('所选学校没有年级数据');
        }
    } catch (error) {
        console.error('加载年级选项失败:', error);
        select.innerHTML = '<option value="">加载失败</option>';
    }
}

/**
 * 加载班级选项
 */
async function loadClassOptions(schoolIds, grades, selectId) {
    console.log('开始加载班级选项，schoolIds:', schoolIds, 'grades:', grades, 'selectId:', selectId);
    const select = document.getElementById(selectId);

    if (!select) {
        console.error('找不到选择器元素:', selectId);
        return;
    }

    if (!schoolIds || schoolIds.length === 0 || !grades || grades.length === 0) {
        select.innerHTML = '<option value="">请先选择学校和年级</option>';
        select.disabled = true;
        return;
    }

    select.innerHTML = '<option value="">加载中...</option>';
    select.disabled = false;

    try {
        // 确保allStudents数组已加载
        if (!allStudents || allStudents.length === 0) {
            console.log('allStudents为空，尝试重新加载');
            allStudents = await loadStudentsFromAPI();
        }

        console.log('可用学生数据总数:', allStudents.length);

        // 基于学生数据生成班级选项
        const classes = new Set();

        allStudents.forEach(student => {
            // 尝试多种可能的字段名
            const studentGrade = parseInt(student.grade || student.student_grade || student.class_grade);
            const studentClass = student.class || student.student_class || student.class_name;
            const studentSchoolId = String(student.school_id || student.school || '');

            console.log('处理学生班级:', {
                grade: studentGrade,
                class: studentClass,
                schoolId: studentSchoolId,
                selectedSchools: schoolIds,
                selectedGrades: grades
            });

            // 检查学生是否属于所选学校和年级（支持多种数据类型匹配）
            const schoolMatches = schoolIds.some(selectedId => {
                return String(selectedId) === studentSchoolId ||
                       parseInt(selectedId) === parseInt(studentSchoolId);
            });

            const gradeMatches = grades.some(selectedGrade => {
                return String(selectedGrade) === String(studentGrade) ||
                       parseInt(selectedGrade) === parseInt(studentGrade);
            });

            if (schoolMatches && gradeMatches && studentClass) {
                const classNum = parseInt(studentClass);
                if (!isNaN(classNum)) {
                    classes.add(classNum);
                    console.log('添加班级:', classNum);
                }
            }
        });

        console.log('找到的班级:', Array.from(classes));

        if (classes.size > 0) {
            const sortedClasses = Array.from(classes).sort((a, b) => a - b);
            select.innerHTML = sortedClasses.map(className =>
                `<option value="${className}">${className}班</option>`
            ).join('');
            console.log('班级选项加载成功，共', classes.size, '个班级');
        } else {
            select.innerHTML = '<option value="">暂无班级数据</option>';
            console.log('所选学校和年级没有班级数据');
        }
    } catch (error) {
        console.error('加载班级选项失败:', error);
        select.innerHTML = '<option value="">加载失败</option>';
    }
}

/**
 * 获取教师任教配置数据
 */
function getTeacherAssignmentData(containerId) {
    console.log('获取教师任教配置数据，容器ID:', containerId);
    const container = document.getElementById(containerId);

    if (!container) {
        console.error('找不到容器元素:', containerId);
        return [];
    }

    const assignments = [];

    // 遍历每个学校的配置卡片
    const cards = container.querySelectorAll('.school-assignment-card');
    console.log('找到配置卡片数量:', cards.length);

    cards.forEach((card, index) => {
        console.log(`处理第${index + 1}个配置卡片`);

        const gradeSelect = card.querySelector('.school-grades');
        const classSelect = card.querySelector('.school-classes');

        if (!gradeSelect || !classSelect) {
            console.log('配置卡片缺少选择器元素');
            return;
        }

        const schoolId = gradeSelect.dataset.schoolId;
        const schoolName = card.querySelector('h6').textContent.replace(/.*配置中/, '').trim();

        const selectedGrades = Array.from(gradeSelect.selectedOptions).map(option => option.value);
        const selectedClasses = Array.from(classSelect.selectedOptions).map(option => option.value);

        console.log('学校配置:', {
            schoolId,
            schoolName,
            selectedGrades,
            selectedClasses
        });

        // 为每个年级和班级的组合创建一个任教记录
        selectedGrades.forEach(grade => {
            selectedClasses.forEach(className => {
                assignments.push({
                    school_id: schoolId,
                    school_name: schoolName,
                    grade: parseInt(grade),
                    class: parseInt(className)
                });
            });
        });
    });

    console.log('最终获取到的任教配置:', assignments);
    return assignments;
}

/**
 * 确认添加教师
 */
async function confirmAddTeacher() {
    const username = document.getElementById('teacherUsername').value.trim();
    const password = document.getElementById('teacherPassword').value.trim();
    const displayName = document.getElementById('teacherDisplayName').value.trim();

    if (!username || !password || !displayName) {
        Utils.showMessage('请填写所有必填字段', 'error');
        return;
    }

    // 获取任教配置数据
    const assignments = getTeacherAssignmentData('teacherAssignmentContainer');
    console.log('教师任教配置:', assignments);

    // 显示加载状态
    const spinner = document.getElementById('addTeacherSpinner');
    const button = spinner.parentElement;
    spinner.classList.remove('d-none');
    button.disabled = true;

    try {
        // 使用新的管理员API添加教师
        const response = await fetch('/api/admin/teachers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username,
                password,
                display_name: displayName,
                assignments: assignments // 包含任教配置
            })
        });

        const result = await response.json();

        if (result.success) {
            Utils.showMessage('教师添加成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addTeacherModal')).hide();

            // 重新加载教师数据并刷新列表
            allTeachers = await loadTeachersFromAPI();
            showAdminSection('teachers');
        } else {
            Utils.showMessage(result.message || '添加教师失败', 'error');
        }
    } catch (error) {
        console.error('添加教师失败:', error);
        Utils.showMessage('添加教师失败: ' + error.message, 'error');
    } finally {
        // 隐藏加载状态
        spinner.classList.add('d-none');
        button.disabled = false;
    }
}





/**
 * 删除教师
 */
async function deleteTeacher(id) {
    const teacher = allTeachers.find(t => t.id === id);
    if (!teacher) {
        Utils.showMessage('教师信息不存在', 'error');
        return;
    }

    if (!confirm(`确定要删除教师 "${teacher.displayName || teacher.username}" 吗？此操作不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/teachers/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('教师删除成功', 'success');
            showAdminSection('teachers'); // 刷新教师列表
        } else {
            Utils.showMessage(result.message || '删除教师失败', 'error');
        }
    } catch (error) {
        console.error('删除教师失败:', error);
        Utils.showMessage('删除教师失败，请重试', 'error');
    }
}

/**
 * 显示添加学生表单
 */
function showAddStudentForm() {
    Utils.showMessage('请使用教师管理界面的批量导入功能添加学生', 'info');
}

/**
 * 编辑学生
 */
async function editStudent(id) {
    Utils.showMessage('学生信息编辑功能请在教师管理界面中使用', 'info');
}

/**
 * 删除学生
 */
async function deleteStudent(id) {
    const student = allStudents.find(s => s.id === id);
    if (!student) {
        Utils.showMessage('学生信息不存在', 'error');
        return;
    }

    if (!confirm(`确定要删除学生 "${student.name}" (学号: ${student.studentId}) 吗？此操作不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/students/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学生删除成功', 'success');
            showAdminSection('students'); // 刷新学生列表
        } else {
            Utils.showMessage(result.message || '删除学生失败', 'error');
        }
    } catch (error) {
        console.error('删除学生失败:', error);
        Utils.showMessage('删除学生失败，请重试', 'error');
    }
}

/**
 * 显示添加学校表单
 */
function showAddSchoolForm() {
    const name = prompt('请输入学校名称:');
    if (!name || !name.trim()) {
        return;
    }

    const address = prompt('请输入学校地址 (可选):') || '';

    addSchool(name.trim(), address.trim());
}

/**
 * 添加学校
 */
async function addSchool(name, address) {
    try {
        const response = await fetch('/api/admin/schools', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ name, address })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学校添加成功', 'success');
            // 重新加载学校数据并刷新列表
            allSchools = await loadSchoolsFromAPI();
            showAdminSection('schools');
        } else {
            Utils.showMessage(result.message || '添加学校失败', 'error');
        }
    } catch (error) {
        console.error('添加学校失败:', error);
        Utils.showMessage('添加学校失败，请重试', 'error');
    }
}

/**
 * 编辑学校
 */
async function editSchool(id) {
    const school = allSchools.find(s => s.id === id);
    if (!school) {
        Utils.showMessage('学校信息不存在', 'error');
        return;
    }

    const name = prompt('请输入学校名称:', school.name);
    if (!name || !name.trim()) {
        return;
    }

    const address = prompt('请输入学校地址:', school.address || '') || '';

    try {
        const response = await fetch(`/api/admin/schools/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ name: name.trim(), address: address.trim() })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学校信息更新成功', 'success');
            // 重新加载学校数据并刷新列表
            allSchools = await loadSchoolsFromAPI();
            showAdminSection('schools');
        } else {
            Utils.showMessage(result.message || '更新学校信息失败', 'error');
        }
    } catch (error) {
        console.error('更新学校失败:', error);
        Utils.showMessage('更新学校信息失败，请重试', 'error');
    }
}

/**
 * 删除学校
 */
async function deleteSchool(id) {
    const school = allSchools.find(s => s.id === id);
    if (!school) {
        Utils.showMessage('学校信息不存在', 'error');
        return;
    }

    if (!confirm(`确定要删除学校 "${school.name}" 吗？此操作将同时删除该学校下的所有班级和学生，且不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/schools/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('学校删除成功', 'success');
            // 重新加载学校数据并刷新列表
            allSchools = await loadSchoolsFromAPI();
            showAdminSection('schools');
        } else {
            Utils.showMessage(result.message || '删除学校失败', 'error');
        }
    } catch (error) {
        console.error('删除学校失败:', error);
        Utils.showMessage('删除学校失败，请重试', 'error');
    }
}

/**
 * 显示添加班级表单
 */
function showAddClassForm() {
    if (allSchools.length === 0) {
        Utils.showMessage('请先添加学校', 'error');
        return;
    }

    const schoolOptions = allSchools.map(school => `${school.id}:${school.name}`).join('\n');
    const schoolInput = prompt(`请选择学校 (输入学校ID):\n${schoolOptions}`);

    if (!schoolInput) return;

    const schoolId = parseInt(schoolInput);
    const school = allSchools.find(s => s.id === schoolId);

    if (!school) {
        Utils.showMessage('无效的学校ID', 'error');
        return;
    }

    const grade = prompt('请输入年级 (1-12):');
    if (!grade || isNaN(grade) || grade < 1 || grade > 12) {
        Utils.showMessage('请输入有效的年级 (1-12)', 'error');
        return;
    }

    const classNumber = prompt('请输入班级号 (1-50):');
    if (!classNumber || isNaN(classNumber) || classNumber < 1 || classNumber > 50) {
        Utils.showMessage('请输入有效的班级号 (1-50)', 'error');
        return;
    }

    addClass(schoolId, parseInt(grade), parseInt(classNumber));
}

/**
 * 添加班级
 */
async function addClass(schoolId, grade, classNumber) {
    try {
        const response = await fetch('/api/admin/classes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ schoolId, grade, classNumber })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('班级添加成功', 'success');
            showAdminSection('classes'); // 刷新班级列表
        } else {
            Utils.showMessage(result.message || '添加班级失败', 'error');
        }
    } catch (error) {
        console.error('添加班级失败:', error);
        Utils.showMessage('添加班级失败，请重试', 'error');
    }
}

/**
 * 编辑班级
 */
async function editClass(id) {
    Utils.showMessage('班级信息编辑功能暂不支持，请删除后重新创建', 'info');
}

/**
 * 删除班级
 */
async function deleteClass(id) {
    const classInfo = allClasses.find(c => c.id === id);
    if (!classInfo) {
        Utils.showMessage('班级信息不存在', 'error');
        return;
    }

    const className = `${classInfo.grade}年级${classInfo.classNumber}班`;
    if (!confirm(`确定要删除班级 "${className}" 吗？此操作将同时删除该班级下的所有学生，且不可恢复。`)) {
        return;
    }

    try {
        const response = await fetch(`/api/admin/classes/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('班级删除成功', 'success');
            showAdminSection('classes'); // 刷新班级列表
        } else {
            Utils.showMessage(result.message || '删除班级失败', 'error');
        }
    } catch (error) {
        console.error('删除班级失败:', error);
        Utils.showMessage('删除班级失败，请重试', 'error');
    }
}

/**
 * 管理教师权限
 */
async function manageTeacherPermissions(teacherId) {
    const teacher = allTeachers.find(t => t.id === teacherId);
    if (!teacher) {
        Utils.showMessage('教师信息不存在', 'error');
        return;
    }

    try {
        // 获取教师当前权限
        const response = await fetch(`/api/admin/teachers/${teacherId}/permissions`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        let permissions = [];
        if (response.ok) {
            const result = await response.json();
            permissions = result.data || [];
        }

        // 显示权限管理对话框
        const permissionOptions = [
            { key: 'manage_students', label: '学生管理' },
            { key: 'manage_classes', label: '班级管理' },
            { key: 'manage_schools', label: '学校管理' },
            { key: 'view_reports', label: '查看报告' },
            { key: 'export_data', label: '数据导出' }
        ];

        let permissionHtml = `
            <div style="text-align: left;">
                <h4>管理教师权限: ${teacher.displayName || teacher.username}</h4>
                <form id="permissionForm">
        `;

        permissionOptions.forEach(option => {
            const checked = permissions.includes(option.key) ? 'checked' : '';
            permissionHtml += `
                <div style="margin: 10px 0;">
                    <label>
                        <input type="checkbox" name="permissions" value="${option.key}" ${checked}>
                        ${option.label}
                    </label>
                </div>
            `;
        });

        permissionHtml += `
                </form>
                <div style="margin-top: 20px; text-align: center;">
                    <button onclick="saveTeacherPermissions(${teacherId})" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px; margin-right: 10px;">
                        保存权限
                    </button>
                    <button onclick="closePermissionDialog()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 5px;">
                        取消
                    </button>
                </div>
            </div>
        `;

        // 创建模态对话框
        const dialog = document.createElement('div');
        dialog.id = 'permissionDialog';
        dialog.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
            justify-content: center; align-items: center;
        `;

        const dialogContent = document.createElement('div');
        dialogContent.style.cssText = `
            background: white; padding: 30px; border-radius: 15px;
            max-width: 500px; width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;
        dialogContent.innerHTML = permissionHtml;

        dialog.appendChild(dialogContent);
        document.body.appendChild(dialog);

    } catch (error) {
        console.error('获取教师权限失败:', error);
        Utils.showMessage('获取教师权限失败', 'error');
    }
}

/**
 * 保存教师权限
 */
async function saveTeacherPermissions(teacherId) {
    try {
        const form = document.getElementById('permissionForm');
        const formData = new FormData(form);
        const permissions = formData.getAll('permissions');

        const response = await fetch(`/api/admin/teachers/${teacherId}/permissions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ permissions })
        });

        const result = await response.json();

        if (response.ok) {
            Utils.showMessage('权限更新成功', 'success');
            closePermissionDialog();
        } else {
            Utils.showMessage(result.message || '权限更新失败', 'error');
        }
    } catch (error) {
        console.error('保存权限失败:', error);
        Utils.showMessage('保存权限失败，请重试', 'error');
    }
}

/**
 * 关闭权限对话框
 */
function closePermissionDialog() {
    const dialog = document.getElementById('permissionDialog');
    if (dialog) {
        dialog.remove();
    }
}

/**
 * 编辑学生信息
 */
async function editStudent(studentId) {
    const student = allStudents.find(s => (s.id || s.student_id) == studentId);
    if (!student) {
        Utils.showMessage('学生信息不存在', 'error');
        return;
    }

    const studentName = student.name || student.student_name || '未知';
    const newName = prompt(`编辑学生姓名:`, studentName);

    if (newName && newName !== studentName) {
        try {
            const response = await fetch(`/api/admin/students/${studentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: newName
                })
            });

            const result = await response.json();

            if (result.success) {
                Utils.showMessage('学生信息更新成功', 'success');
                // 重新加载学生数据
                allStudents = await loadStudentsFromAPI();
                showAdminSection('students');
            } else {
                Utils.showMessage(result.message || '更新学生信息失败', 'error');
            }
        } catch (error) {
            console.error('更新学生失败:', error);
            Utils.showMessage('更新学生信息失败: ' + error.message, 'error');
        }
    }
}

/**
 * 删除学生
 */
async function deleteStudent(studentId) {
    const student = allStudents.find(s => (s.id || s.student_id) == studentId);
    if (!student) {
        Utils.showMessage('学生信息不存在', 'error');
        return;
    }

    const studentName = student.name || student.student_name || '未知';
    if (confirm(`确定要删除学生 ${studentName} 吗？此操作不可撤销！`)) {
        try {
            const response = await fetch(`/api/admin/students/${studentId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                Utils.showMessage('学生删除成功', 'success');
                // 重新加载学生数据
                allStudents = await loadStudentsFromAPI();
                showAdminSection('students');
            } else {
                Utils.showMessage(result.message || '删除学生失败', 'error');
            }
        } catch (error) {
            console.error('删除学生失败:', error);
            Utils.showMessage('删除学生失败: ' + error.message, 'error');
        }
    }
}

/**
 * 查看教师权限详情
 */
async function viewTeacherPermissions(teacherId) {
    const teacher = allTeachers.find(t => t.id == teacherId);
    if (!teacher) {
        Utils.showMessage('教师信息不存在', 'error');
        return;
    }

    // 显示加载提示
    Utils.showMessage('正在加载教师权限信息...', 'info');

    try {
        // 获取教师详细权限信息
        const response = await fetch(`/api/admin/teachers/${teacherId}/permissions`);
        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || '获取权限信息失败');
        }

        // 显示权限详情模态框
        showTeacherPermissionsModal(teacher, result);

    } catch (error) {
        console.error('获取教师权限失败:', error);
        if (error.message.includes('fetch')) {
            Utils.showMessage('网络连接失败，请检查网络后重试', 'error');
        } else {
            Utils.showMessage('获取教师权限失败: ' + error.message, 'error');
        }
    }
}

/**
 * 显示教师权限详情模态框
 */
function showTeacherPermissionsModal(teacher, permissions) {
    const { schoolAssignments, classPermissions } = permissions;

    // 构建权限详情HTML
    let permissionsHtml = '';

    if (schoolAssignments && schoolAssignments.length > 0) {
        permissionsHtml += '<h6 class="mb-3"><i class="fas fa-school"></i> 任教学校</h6>';
        permissionsHtml += '<div class="row mb-4">';
        schoolAssignments.forEach(assignment => {
            permissionsHtml += `
                <div class="col-md-6 mb-2">
                    <div class="card border-primary">
                        <div class="card-body py-2">
                            <h6 class="card-title mb-1">${assignment.schools?.name || '未知学校'}</h6>
                            <small class="text-muted">分配时间: ${new Date(assignment.created_at).toLocaleDateString()}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        permissionsHtml += '</div>';
    }

    if (classPermissions && classPermissions.length > 0) {
        permissionsHtml += '<h6 class="mb-3"><i class="fas fa-users"></i> 任教班级</h6>';

        // 按学校分组显示班级权限
        const groupedBySchool = classPermissions.reduce((acc, cp) => {
            const schoolName = cp.schools?.name || '未知学校';
            if (!acc[schoolName]) {
                acc[schoolName] = [];
            }
            acc[schoolName].push(cp);
            return acc;
        }, {});

        Object.keys(groupedBySchool).forEach(schoolName => {
            permissionsHtml += `
                <div class="mb-3">
                    <h6 class="text-primary">${schoolName}</h6>
                    <div class="row">
            `;

            groupedBySchool[schoolName].forEach(cp => {
                permissionsHtml += `
                    <div class="col-md-4 mb-2">
                        <span class="badge bg-success">${cp.grade}年级${cp.class}班</span>
                    </div>
                `;
            });

            permissionsHtml += '</div></div>';
        });
    }

    if (!schoolAssignments?.length && !classPermissions?.length) {
        permissionsHtml = `
            <div class="text-center py-4">
                <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                <h6>暂无任教权限配置</h6>
                <p class="text-muted">该教师尚未配置任何学校或班级权限</p>
                <div class="mt-3">
                    <button class="btn btn-primary btn-sm" onclick="editTeacher('${teacher.id}')">
                        <i class="fas fa-plus"></i> 立即配置权限
                    </button>
                </div>
            </div>
        `;
    } else {
        // 添加权限统计信息
        const schoolCount = schoolAssignments?.length || 0;
        const classCount = classPermissions?.length || 0;

        permissionsHtml = `
            <div class="alert alert-info mb-4">
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="mb-1">${schoolCount}</h5>
                        <small>任教学校</small>
                    </div>
                    <div class="col-6">
                        <h5 class="mb-1">${classCount}</h5>
                        <small>任教班级</small>
                    </div>
                </div>
            </div>
        ` + permissionsHtml;
    }

    // 创建并显示模态框
    const modalHtml = `
        <div class="modal fade" id="teacherPermissionsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-shield"></i>
                            ${teacher.display_name || teacher.username} 的任教权限
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${permissionsHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="editTeacher('${teacher.id}')">
                            <i class="fas fa-edit"></i> 编辑权限
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('teacherPermissionsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('teacherPermissionsModal'));
    modal.show();

    // 模态框关闭时清理
    document.getElementById('teacherPermissionsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * 编辑教师信息
 */
async function editTeacher(teacherId) {
    const teacher = allTeachers.find(t => t.id == teacherId);
    if (!teacher) {
        Utils.showMessage('教师信息不存在', 'error');
        return;
    }

    try {
        // 显示加载提示
        Utils.showMessage('正在加载教师信息...', 'info');

        // 填充编辑表单
        document.getElementById('editTeacherId').value = teacher.id;
        document.getElementById('editTeacherUsername').value = teacher.username;
        document.getElementById('editTeacherDisplayName').value = teacher.display_name || '';
        document.getElementById('editTeacherNewPassword').value = ''; // 清空密码字段

        // 加载学校选项
        await loadSchoolOptions('editTeacherSchools');

        // 绑定编辑表单事件
        bindEditTeacherFormEvents();

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editTeacherModal'));

        modal.show();

        // 使用setTimeout来确保模态框完全显示后再加载数据
        setTimeout(async () => {
            try {
                console.log('延迟加载任教数据...');
                await loadTeacherAssignmentData(teacherId);
            } catch (error) {
                console.error('加载任教数据失败:', error);
                Utils.showMessage('加载任教数据失败，请手动配置', 'warning');

                // 显示手动配置界面
                const container = document.getElementById('editTeacherAssignmentConfig');
                if (container) {
                    container.innerHTML = `
                        <div class="text-center text-muted py-5">
                            <div class="mb-3">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning opacity-50"></i>
                            </div>
                            <h6 class="mb-2">加载失败</h6>
                            <p class="mb-0 small">请手动选择学校并配置年级班级信息</p>
                            <button class="btn btn-outline-primary btn-sm mt-3" onclick="document.getElementById('editTeacherSchools').focus()">
                                <i class="fas fa-edit"></i> 手动配置
                            </button>
                        </div>
                    `;
                }
            }
        }, 1000); // 延迟1秒

    } catch (error) {
        console.error('编辑教师失败:', error);
        Utils.showMessage('加载教师信息失败: ' + error.message, 'error');
    }
}

/**
 * 加载教师的真实任教数据
 */
async function loadTeacherAssignmentData(teacherId) {
    console.log('=== 开始加载教师任教数据 ===');
    console.log('教师ID:', teacherId);
    console.log('当前时间:', new Date().toISOString());

    const container = document.getElementById('editTeacherAssignmentConfig');
    console.log('配置容器元素:', container);

    if (!container) {
        console.error('找不到任教配置容器 editTeacherAssignmentConfig');
        console.log('页面中的所有ID包含"edit"的元素:');
        document.querySelectorAll('[id*="edit"]').forEach(el => {
            console.log('- ', el.id, el);
        });
        return;
    }

    // 显示加载状态
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <h6 class="mb-2">正在加载任教信息</h6>
            <p class="text-muted small">请稍候...</p>
        </div>
    `;

    try {
        console.log('尝试从API获取教师权限数据...');
        console.log('API URL:', `/api/admin/teachers/${teacherId}/permissions`);

        // 使用现有的权限API获取教师任教数据
        const response = await fetch(`/api/admin/teachers/${teacherId}/permissions`);

        console.log('API响应状态:', response.status);
        console.log('API响应头:', response.headers);
        console.log('API响应对象:', response);

        if (response.ok) {
            const result = await response.json();
            console.log('教师权限数据:', result);
            console.log('数据结构分析:');
            console.log('- result.success:', result.success);
            console.log('- result.data:', result.data);
            console.log('- result.data 的类型:', typeof result.data);
            console.log('- result.data 的键:', result.data ? Object.keys(result.data) : 'null');

            // 修正数据结构解析 - API返回的是 {schools, classes} 而不是 {schoolAssignments, classPermissions}
            const data = result.data || result;
            const schoolAssignments = data.schools || [];
            const classPermissions = data.classes || [];

            console.log('解析后的数据:');
            console.log('- schoolAssignments (schools):', schoolAssignments);
            console.log('- classPermissions (classes):', classPermissions);

            // 根据真实数据设置学校选择和班级配置
            if (schoolAssignments && schoolAssignments.length > 0) {
                console.log('发现任教数据，开始设置学校选择...');
                await setTeacherSchoolSelections(schoolAssignments, classPermissions);
                console.log('任教数据加载完成');
            } else {
                console.log('该教师暂无任教数据，显示空状态');
                // 显示默认提示
                container.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <div class="mb-3">
                            <i class="fas fa-info-circle fa-3x text-info opacity-50"></i>
                        </div>
                        <h6 class="mb-2">暂无任教信息</h6>
                        <p class="mb-0 small">请先在上方选择任教学校，然后配置年级班级</p>
                        <button class="btn btn-outline-primary btn-sm mt-3" onclick="document.getElementById('editTeacherSchools').focus()">
                            <i class="fas fa-plus"></i> 开始配置
                        </button>
                    </div>
                `;
            }
        } else if (response.status === 404) {
            console.log('教师权限数据不存在');
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <div class="mb-3">
                        <i class="fas fa-user-plus fa-3x text-primary opacity-50"></i>
                    </div>
                    <h6 class="mb-2">尚未配置任教信息</h6>
                    <p class="mb-0 small">这是一个新教师，请为其配置任教学校和班级</p>
                    <button class="btn btn-outline-primary btn-sm mt-3" onclick="document.getElementById('editTeacherSchools').focus()">
                        <i class="fas fa-plus"></i> 开始配置
                    </button>
                </div>
            `;
        } else {
            console.log('获取教师权限数据失败，状态码:', response.status);
            const errorText = await response.text();
            container.innerHTML = `
                <div class="text-center text-muted py-5">
                    <div class="mb-3">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning opacity-50"></i>
                    </div>
                    <h6 class="mb-2">加载任教数据失败</h6>
                    <p class="mb-0 small">服务器响应: ${response.status}</p>
                    <button class="btn btn-outline-secondary btn-sm mt-3" onclick="loadTeacherAssignmentData('${teacherId}')">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                    <button class="btn btn-outline-primary btn-sm mt-3 ms-2" onclick="document.getElementById('editTeacherSchools').focus()">
                        <i class="fas fa-edit"></i> 手动配置
                    </button>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载教师任教数据失败:', error);
        // 显示错误提示
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger opacity-50"></i>
                </div>
                <h6 class="mb-2">网络连接失败</h6>
                <p class="mb-0 small">请检查网络连接后重试</p>
                <button class="btn btn-outline-secondary btn-sm mt-3" onclick="loadTeacherAssignmentData('${teacherId}')">
                    <i class="fas fa-redo"></i> 重试
                </button>
                <button class="btn btn-outline-primary btn-sm mt-3 ms-2" onclick="document.getElementById('editTeacherSchools').focus()">
                    <i class="fas fa-edit"></i> 手动配置
                </button>
            </div>
        `;
    }
}



/**
 * 根据任教数据设置学校选择
 */
async function setTeacherSchoolSelections(schoolAssignments, classPermissions) {
    const schoolSelect = document.getElementById('editTeacherSchools');
    const container = document.getElementById('editTeacherAssignmentConfig');

    if (!schoolSelect || !container) {
        console.error('找不到学校选择器或配置容器');
        return;
    }

    console.log('设置教师学校选择，数据:', { schoolAssignments, classPermissions });

    // 提取所有任教学校ID
    const schoolIds = [...new Set(schoolAssignments.map(sa => sa.school_id))];
    console.log('提取的学校ID:', schoolIds);

    // 确保学校选择器有选项（如果没有，等待一下再试）
    if (schoolSelect.options.length <= 1) {
        console.log('学校选择器选项不足，等待加载...');
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 设置学校选择
    let selectedCount = 0;
    Array.from(schoolSelect.options).forEach(option => {
        const optionValue = parseInt(option.value);
        if (schoolIds.includes(optionValue)) {
            option.selected = true;
            selectedCount++;
            console.log(`选中学校: ${option.text} (ID: ${optionValue})`);
        }
    });

    console.log(`共选中 ${selectedCount} 个学校`);

    // 生成学校配置界面
    const selectedSchools = schoolIds.map(schoolId => {
        const assignment = schoolAssignments.find(sa => sa.school_id === schoolId);
        return {
            id: schoolId,
            name: assignment?.schools?.name || `学校${schoolId}`
        };
    });

    if (selectedSchools.length > 0) {
        console.log('生成任教配置界面，学校:', selectedSchools);
        await generateEditTeacherAssignmentInterface(selectedSchools, container, classPermissions);
    } else {
        console.log('没有选中的学校，显示空状态');
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <div class="mb-3">
                    <i class="fas fa-info-circle fa-3x text-info opacity-50"></i>
                </div>
                <h6 class="mb-2">暂无任教信息</h6>
                <p class="mb-0 small">请先在上方选择任教学校，然后配置年级班级</p>
                <button class="btn btn-outline-primary btn-sm mt-3" onclick="document.getElementById('editTeacherSchools').focus()">
                    <i class="fas fa-plus"></i> 开始配置
                </button>
            </div>
        `;
    }
}

/**
 * 生成编辑教师任教配置界面
 */
async function generateEditTeacherAssignmentInterface(selectedSchools, container, classPermissions) {
    if (selectedSchools.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-5">
                <div class="mb-3">
                    <i class="fas fa-arrow-up fa-3x text-primary opacity-50"></i>
                </div>
                <h6 class="mb-2">配置任教信息</h6>
                <p class="mb-0 small">请先在上方选择任教学校，系统将为每个学校生成独立的年级班级配置区域</p>
            </div>
        `;
        return;
    }

    let html = '';

    for (const school of selectedSchools) {
        // 获取该学校的班级权限
        const schoolClassPermissions = classPermissions?.filter(cp => cp.school_id === school.id) || [];

        html += `
            <div class="card mb-4" data-school-id="${school.id}">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-school"></i> ${school.name}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">任教年级 *</label>
                            <select class="form-select school-grades" data-school-id="${school.id}" multiple size="6">
                                <option value="1">一年级</option>
                                <option value="2">二年级</option>
                                <option value="3">三年级</option>
                                <option value="4">四年级</option>
                                <option value="5">五年级</option>
                                <option value="6">六年级</option>
                            </select>
                            <div class="form-text">按住Ctrl键可选择多个年级</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">任教班级</label>
                            <div class="school-classes-container" data-school-id="${school.id}">
                                <!-- 班级选择将根据年级动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    container.innerHTML = html;

    // 为每个学校设置现有的权限
    for (const school of selectedSchools) {
        const schoolClassPermissions = classPermissions?.filter(cp => cp.school_id === school.id) || [];

        if (schoolClassPermissions.length > 0) {
            // 设置年级选择
            const gradeSelect = container.querySelector(`.school-grades[data-school-id="${school.id}"]`);
            const selectedGrades = [...new Set(schoolClassPermissions.map(cp => cp.grade))];

            Array.from(gradeSelect.options).forEach(option => {
                option.selected = selectedGrades.includes(parseInt(option.value));
            });

            // 生成班级选择界面
            await generateClassSelectionForSchool(school.id, selectedGrades, schoolClassPermissions);
        }

        // 绑定年级变化事件
        const gradeSelect = container.querySelector(`.school-grades[data-school-id="${school.id}"]`);
        gradeSelect.addEventListener('change', function() {
            const selectedGrades = Array.from(this.selectedOptions).map(option => parseInt(option.value));
            generateClassSelectionForSchool(school.id, selectedGrades);
        });
    }
}

/**
 * 为指定学校生成班级选择界面
 */
async function generateClassSelectionForSchool(schoolId, selectedGrades, existingPermissions = []) {
    const container = document.querySelector(`.school-classes-container[data-school-id="${schoolId}"]`);
    if (!container) return;

    if (selectedGrades.length === 0) {
        container.innerHTML = '<p class="text-muted small">请先选择年级</p>';
        return;
    }

    let html = '';

    for (const grade of selectedGrades) {
        const gradePermissions = existingPermissions.filter(ep => ep.grade === grade);
        const selectedClasses = gradePermissions.map(ep => ep.class);

        html += `
            <div class="mb-3">
                <label class="form-label small">${grade}年级班级</label>
                <select class="form-select form-select-sm school-classes" data-school-id="${schoolId}" data-grade="${grade}" multiple size="3">
                    <option value="1" ${selectedClasses.includes(1) ? 'selected' : ''}>1班</option>
                    <option value="2" ${selectedClasses.includes(2) ? 'selected' : ''}>2班</option>
                    <option value="3" ${selectedClasses.includes(3) ? 'selected' : ''}>3班</option>
                    <option value="4" ${selectedClasses.includes(4) ? 'selected' : ''}>4班</option>
                    <option value="5" ${selectedClasses.includes(5) ? 'selected' : ''}>5班</option>
                    <option value="6" ${selectedClasses.includes(6) ? 'selected' : ''}>6班</option>
                </select>
            </div>
        `;
    }

    container.innerHTML = html;
}

/**
 * 绑定编辑教师表单事件
 */
function bindEditTeacherFormEvents() {
    console.log('绑定编辑教师表单事件');

    const schoolSelect = document.getElementById('editTeacherSchools');
    const assignmentContainer = document.getElementById('editTeacherAssignmentConfig');

    if (!schoolSelect || !assignmentContainer) {
        console.error('找不到学校选择器或配置容器');
        return;
    }

    // 当学校选择改变时，重新生成分层级配置界面
    schoolSelect.addEventListener('change', () => {
        console.log('编辑模式：学校选择改变');
        const selectedSchools = Array.from(schoolSelect.selectedOptions).map(option => ({
            id: parseInt(option.value),
            name: option.text
        })).filter(school => school.id);

        console.log('编辑模式：选中的学校:', selectedSchools);

        if (selectedSchools.length > 0) {
            generateEditTeacherAssignmentInterface(selectedSchools, assignmentContainer, []);
        } else {
            // 显示默认提示
            assignmentContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                    <p>请先选择任教学校，然后为每个学校配置年级和班级</p>
                </div>
            `;
        }
    });
}

/**
 * 获取教师任教配置数据
 */
function getEditTeacherAssignmentData() {
    const container = document.getElementById('editTeacherAssignmentConfig');
    if (!container) return { schools: [], classes: [] };

    const schoolCards = container.querySelectorAll('.card[data-school-id]');
    const schools = [];
    const classes = [];

    schoolCards.forEach(card => {
        const schoolId = parseInt(card.dataset.schoolId);
        if (schoolId) {
            schools.push(schoolId);

            // 获取该学校的班级权限
            const classSelects = card.querySelectorAll('.school-classes');
            classSelects.forEach(select => {
                const grade = parseInt(select.dataset.grade);
                const selectedClasses = Array.from(select.selectedOptions).map(option => parseInt(option.value));

                selectedClasses.forEach(classNum => {
                    classes.push({
                        school_id: schoolId,
                        grade: grade,
                        class: classNum
                    });
                });
            });
        }
    });

    return { schools, classes };
}

/**
 * 确认编辑教师
 */
async function confirmEditTeacher() {
    const teacherId = document.getElementById('editTeacherId').value;
    const displayName = document.getElementById('editTeacherDisplayName').value.trim();
    const newPassword = document.getElementById('editTeacherNewPassword').value;

    // 验证输入
    if (!teacherId) {
        Utils.showMessage('教师ID不能为空', 'error');
        return;
    }

    if (!displayName) {
        Utils.showMessage('显示名称不能为空', 'error');
        document.getElementById('editTeacherDisplayName').focus();
        return;
    }

    if (displayName.length < 2) {
        Utils.showMessage('显示名称至少需要2个字符', 'error');
        document.getElementById('editTeacherDisplayName').focus();
        return;
    }

    // 验证密码（如果提供）
    if (newPassword && newPassword.trim()) {
        if (newPassword.trim().length < 6) {
            Utils.showMessage('新密码至少需要6个字符', 'error');
            document.getElementById('editTeacherNewPassword').focus();
            return;
        }
    }

    // 显示加载状态
    const spinner = document.getElementById('editTeacherSpinner');
    const button = spinner?.parentElement;
    if (spinner) spinner.classList.remove('d-none');
    if (button) button.disabled = true;

    // 获取任教配置数据
    const assignmentData = getEditTeacherAssignmentData();
    console.log('保存教师任教配置:', assignmentData);

    try {
        // 准备更新数据
        const updateData = {
            display_name: displayName
        };

        // 如果有新密码，添加到更新数据中
        if (newPassword && newPassword.trim()) {
            updateData.password = newPassword.trim();
        }

        // 更新教师基本信息
        const basicResponse = await fetch(`/api/admin/teachers/${teacherId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });

        if (!basicResponse.ok) {
            const errorText = await basicResponse.text();
            throw new Error(`HTTP ${basicResponse.status}: ${errorText}`);
        }

        const basicResult = await basicResponse.json();

        if (!basicResult.success) {
            throw new Error(basicResult.message || '更新教师基本信息失败');
        }

        // 更新教师权限配置（只有在有配置数据时才更新）
        if (assignmentData.schools.length > 0 || assignmentData.classes.length > 0) {
            const permissionResponse = await fetch(`/api/admin/teachers/${teacherId}/permissions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(assignmentData)
            });

            if (!permissionResponse.ok) {
                const errorText = await permissionResponse.text();
                throw new Error(`权限更新失败 HTTP ${permissionResponse.status}: ${errorText}`);
            }

            const permissionResult = await permissionResponse.json();

            if (!permissionResult.success) {
                throw new Error(permissionResult.message || '更新教师权限配置失败');
            }
        }

        Utils.showMessage('教师信息和任教配置更新成功', 'success');
        bootstrap.Modal.getInstance(document.getElementById('editTeacherModal')).hide();

        // 重新加载教师数据
        try {
            allTeachers = await loadTeachersFromAPI();
            showAdminSection('teachers');
        } catch (reloadError) {
            console.error('重新加载教师数据失败:', reloadError);
            Utils.showMessage('更新成功，但刷新列表失败，请手动刷新页面', 'warning');
        }

    } catch (error) {
        console.error('更新教师失败:', error);

        // 根据错误类型提供不同的提示
        if (error.message.includes('网络') || error.message.includes('fetch')) {
            Utils.showMessage('网络连接失败，请检查网络后重试', 'error');
        } else if (error.message.includes('权限')) {
            Utils.showMessage('基本信息更新成功，但权限配置更新失败: ' + error.message, 'warning');
        } else {
            Utils.showMessage('更新教师信息失败: ' + error.message, 'error');
        }
    } finally {
        // 隐藏加载状态
        if (spinner) spinner.classList.add('d-none');
        if (button) button.disabled = false;
    }
}

/**
 * 重置教师密码
 */
function resetTeacherPassword(teacherId, teacherName) {
    document.getElementById('resetPasswordTeacherId').value = teacherId;
    document.getElementById('resetPasswordTeacherName').textContent = teacherName;
    document.getElementById('newPasswordDisplay').classList.add('d-none');
    document.getElementById('resetPasswordBtn').style.display = 'inline-block';

    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

/**
 * 确认重置密码
 */
async function confirmResetPassword() {
    const teacherId = document.getElementById('resetPasswordTeacherId').value;

    // 显示加载状态
    const spinner = document.getElementById('resetPasswordSpinner');
    const button = document.getElementById('resetPasswordBtn');
    spinner.classList.remove('d-none');
    button.disabled = true;

    try {
        // 生成新密码
        const newPassword = generateRandomPassword();

        const response = await fetch(`/api/admin/teachers/${teacherId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        });

        const result = await response.json();

        if (result.success) {
            // 显示新密码
            document.getElementById('newPasswordText').textContent = newPassword;
            document.getElementById('newPasswordDisplay').classList.remove('d-none');
            button.style.display = 'none';

            Utils.showMessage('密码重置成功', 'success');
        } else {
            Utils.showMessage(result.message || '重置密码失败', 'error');
        }
    } catch (error) {
        console.error('重置密码失败:', error);
        Utils.showMessage('重置密码失败: ' + error.message, 'error');
    } finally {
        // 隐藏加载状态
        spinner.classList.add('d-none');
        button.disabled = false;
    }
}

/**
 * 生成随机密码
 */
function generateRandomPassword() {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
}

/**
 * 复制密码
 */
function copyPassword() {
    const passwordText = document.getElementById('newPasswordText').textContent;
    navigator.clipboard.writeText(passwordText).then(() => {
        Utils.showMessage('密码已复制到剪贴板', 'success');
    }).catch(() => {
        Utils.showMessage('复制失败，请手动复制', 'error');
    });
}

/**
 * 删除教师
 */
function deleteTeacher(teacherId, teacherName) {
    if (confirm(`确定要删除教师 ${teacherName} 吗？\n\n此操作不可撤销！删除后该教师将无法登录系统。`)) {
        performDeleteTeacher(teacherId);
    }
}

/**
 * 执行删除教师操作
 */
async function performDeleteTeacher(teacherId) {
    try {
        const response = await fetch(`/api/admin/teachers/${teacherId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            Utils.showMessage('教师删除成功', 'success');
            // 重新加载教师数据
            allTeachers = await loadTeachersFromAPI();
            showAdminSection('teachers');
        } else {
            Utils.showMessage(result.message || '删除教师失败', 'error');
        }
    } catch (error) {
        console.error('删除教师失败:', error);
        Utils.showMessage('删除教师失败: ' + error.message, 'error');
    }
}

/**
 * 管理员退出登录
 */
function adminLogout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除用户数据
        localStorage.removeItem(CONFIG.STORAGE.USER);

        // 跳转到首页
        window.location.href = '/';
    }
}
