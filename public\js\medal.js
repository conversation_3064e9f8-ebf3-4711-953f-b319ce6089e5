/**
 * 奖章管理模块
 * 管理学生奖章数据的显示、编辑和统计功能
 */
const MedalModule = {
    // 奖章数据
    data: [],
    // 筛选条件
    filter: {
        school_id: '',
        grade: '',
        class: '',
        search: ''
    },
    // 排行榜类型：individual(个人), class(班级), grade(年级)
    rankingType: 'individual',
    // 图表实例
    chart: null,
    // 选中的学生ID集合
    selectedStudents: new Set(),
    
    /**
     * 初始化奖章模块
     */
    init: function() {
        // 加载数据
        this.loadData();
        // 绑定事件
        this.bindEvents();
    },
    
    /**
     * 加载奖章数据
     */
    loadData: function() {
        Utils.showLoading(true);
        
        // 使用数据库API获取奖章数据
        DB.getMedals(this.filter)
            .then(data => {
                console.log('前端收到的奖章数据:', data);
                console.log('第一条数据示例:', data?.[0]);

                // 将数字年级转为中文显示
                this.data = data.map(item => {
                    // 创建数据的副本以避免修改原始引用
                    const newItem = { ...item };

                    // 将数字年级转为中文
                    newItem.grade_display = Utils.numberToGrade(newItem.grade);

                    // 确保medal_count字段存在且为数字
                    if (typeof newItem.medal_count === 'undefined' || newItem.medal_count === null) {
                        newItem.medal_count = 0;
                    }

                    console.log(`学生 ${newItem.name} 的奖章数量: ${newItem.medal_count}`);
                    return newItem;
                }) || [];
                
                // 更新筛选器
                this.updateFilters();
                // 渲染数据
                this.renderData();
                // 更新排行榜
                this.updateRanking();
                
                Utils.showLoading(false);
            })
            .catch(error => {
                console.error('加载奖章数据失败:', error);
                Utils.showMessage('加载数据失败，请刷新重试');
                Utils.showLoading(false);
            });
    },
    
    /**
     * 绑定事件处理
     */
    bindEvents: function() {
        // 学校筛选
        document.getElementById('medal-school-filter').addEventListener('change', (e) => {
            this.filter.school_id = e.target.value;
            // 重置下级筛选条件
            this.filter.grade = '';
            this.filter.class = '';
            // 更新筛选器状态
            this.updateGradeFilter();
            this.updateClassFilter();
            this.renderData();
        });

        // 年级筛选
        document.getElementById('medal-grade-filter').addEventListener('change', (e) => {
            this.filter.grade = e.target.value;
            // 重置班级筛选条件
            this.filter.class = '';
            // 更新班级筛选器
            this.updateClassFilter();
            this.renderData();
        });

        // 班级筛选
        document.getElementById('medal-class-filter').addEventListener('change', (e) => {
            this.filter.class = e.target.value;
            this.renderData();
        });

        // 搜索
        document.getElementById('medal-search').addEventListener('input', (e) => {
            this.filter.search = e.target.value.trim().toLowerCase();
            this.renderData();
        });

        // 重置筛选
        document.getElementById('medal-reset-filter').addEventListener('click', this.resetFilters.bind(this));

        // 导出数据
        document.getElementById('medal-export').addEventListener('click', this.handleExport.bind(this));
        
        // 刷新数据按钮
        document.getElementById('medal-refresh').addEventListener('click', () => {
            // 刷新数据
            this.loadData();
            // 显示提示信息
            Utils.showMessage('数据已刷新');
        });
        
        // 排行榜类型切换
        document.getElementById('medal-ranking-type').addEventListener('change', (e) => {
            this.rankingType = e.target.value;
            this.updateRanking();
        });
        
        // 为表格区域添加事件委托，处理加减奖章按钮点击
        document.getElementById('medal-data').addEventListener('click', (e) => {
            // 检查点击的是否为按钮
            if (e.target.classList.contains('action-btn')) {
                const id = e.target.getAttribute('data-id');
                const action = e.target.getAttribute('data-action');
                
                // 在控制台打印ID，用于调试
                console.log('单个操作ID:', id, '操作类型:', action);
                
                if (action === 'increase') {
                    this.updateMedalCount(id, 1);
                } else if (action === 'decrease') {
                    this.updateMedalCount(id, -1);
                }
            }
            
            // 处理复选框点击
            if (e.target.type === 'checkbox' && e.target.classList.contains('student-checkbox')) {
                const id = e.target.getAttribute('data-id');
                
                // 在控制台打印ID，用于调试
                console.log('复选框操作ID:', id, '勾选状态:', e.target.checked);
                
                if (e.target.checked) {
                    this.selectedStudents.add(id);
                } else {
                    this.selectedStudents.delete(id);
                }
                this.updateSelectAllCheckbox();
            }
        });
        
        // 全选按钮
        document.getElementById('medal-select-all').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('#medal-data .student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const id = checkbox.getAttribute('data-id');
                if (e.target.checked) {
                    this.selectedStudents.add(id);
                } else {
                    this.selectedStudents.delete(id);
                }
            });
        });
        
        // 全选按钮（按钮形式）
        document.getElementById('select-all-medal').addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('#medal-data .student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                const id = checkbox.getAttribute('data-id');
                this.selectedStudents.add(id);
            });
            document.getElementById('medal-select-all').checked = checkboxes.length > 0;
        });
        
        // 取消全选按钮
        document.getElementById('deselect-all-medal').addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('#medal-data .student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                const id = checkbox.getAttribute('data-id');
                this.selectedStudents.delete(id);
            });
            document.getElementById('medal-select-all').checked = false;
        });
        
        // 批量增加奖章
        document.getElementById('batch-increase-medal').addEventListener('click', () => {
            this.batchUpdateMedals(1);
        });
        
        // 批量减少奖章
        document.getElementById('batch-decrease-medal').addEventListener('click', () => {
            this.batchUpdateMedals(-1);
        });
        
        // 添加学生对话框事件处理
        const modal = document.getElementById('add-student-modal');
        const closeBtn = modal.querySelector('.close');
        const cancelBtn = document.getElementById('add-student-cancel');
        const saveBtn = document.getElementById('add-student-save');
        
        // 关闭对话框
        closeBtn.addEventListener('click', this.closeAddStudentModal.bind(this));
        cancelBtn.addEventListener('click', this.closeAddStudentModal.bind(this));
        
        // 保存新学生
        saveBtn.addEventListener('click', this.saveNewStudent.bind(this));
    },
    
    /**
     * 更新全选复选框状态
     */
    updateSelectAllCheckbox: function() {
        const checkboxes = document.querySelectorAll('#medal-data .student-checkbox');
        const checkedAll = checkboxes.length > 0 && 
                           Array.from(checkboxes).every(checkbox => checkbox.checked);
        document.getElementById('medal-select-all').checked = checkedAll;
    },
    
    /**
     * 获取当前可见的学生ID
     * @returns {Array} - 当前显示的学生ID数组
     */
    getVisibleStudentIds: function() {
        const filteredData = this.getFilteredData();
        return filteredData.map(item => item.student_identifier);
    },
    
    /**
     * 获取当前选中的可见学生ID
     * @returns {Array} - 当前选中的可见学生ID数组
     */
    getSelectedVisibleStudentIds: function() {
        const visibleIds = this.getVisibleStudentIds();
        return [...this.selectedStudents].filter(id => visibleIds.includes(id));
    },
    
    /**
     * 批量更新奖章数量
     * @param {number} direction - 奖章变化方向：1为增加，-1为减少
     */
    batchUpdateMedals: async function(direction) {
        // 直接从当前DOM中获取选中的复选框，而不是依赖缓存的selectedStudents集合
        const checkedBoxes = document.querySelectorAll('#medal-data .student-checkbox:checked');
        const selectedIds = Array.from(checkedBoxes).map(checkbox => checkbox.getAttribute('data-id'));
        
        // 调试输出选中的ID数量
        console.log('选中的ID数量:', selectedIds.length);
        console.log('选中的ID:', selectedIds);
        
        if (selectedIds.length === 0) {
            Utils.showMessage('请先选择学生');
            return;
        }
        
        const changeCount = parseInt(document.getElementById('medal-change-count').value);
        if (isNaN(changeCount) || changeCount < 1) {
            Utils.showMessage('请输入有效的奖章变更值');
            return;
        }
        
        const changeAmount = direction * changeCount;
        const actionText = direction > 0 ? '增加' : '减少';
        
        // 确认操作 - 使用await等待Promise结果
        const confirmed = await Utils.confirm(`确定要为选中的${selectedIds.length}名学生${actionText}奖章数量 ${changeCount} 个吗？`);
        if (!confirmed) {
            return;
        }

        // 立即更新UI（乐观更新）
        const updatedStudents = [];
        selectedIds.forEach(id => {
            const student = this.data.find(item => item.student_identifier === id);
            if (student) {
                const oldCount = student.medal_count;
                const newCount = Math.max(0, student.medal_count + changeAmount);

                // 保存旧值用于回滚
                updatedStudents.push({ id, student, oldCount, newCount });

                // 立即更新本地数据和UI
                student.medal_count = newCount;
                const medalElement = document.querySelector(`#medal-data tr[data-student-id="${id}"] .medal-count`);
                if (medalElement) {
                    medalElement.textContent = newCount;
                    medalElement.style.backgroundColor = '#e8f5e8';
                    setTimeout(() => {
                        medalElement.style.backgroundColor = '';
                    }, 300);
                }
            }
        });

        // 立即更新排行榜
        this.updateRanking();

        // 清除选择状态
        this.selectedStudents.clear();
        document.querySelectorAll('#medal-data .student-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('medal-select-all').checked = false;

        // 异步发送到服务器（静默更新）
        let successCount = 0;
        let failCount = 0;
        const failedStudents = [];

        try {
            // 使用批量API更新
            await DB.batchUpdateMedals(selectedIds, changeAmount);
            successCount = updatedStudents.length;

            // 显示成功提示
            this.showToast(`${successCount}名学生 ${direction > 0 ? '+' : ''}${changeAmount}🏅`, 'success');
        } catch (error) {
            console.error('批量更新失败，尝试逐个更新:', error);

            // 批量失败时，逐个尝试更新
            for (const { id, student, oldCount, newCount } of updatedStudents) {
                try {
                    await DB.updateMedals(id, newCount);
                    successCount++;
                } catch (individualError) {
                    console.error(`更新学生 ${id} 失败:`, individualError);
                    failCount++;
                    failedStudents.push({ id, student, oldCount });
                }
            }

            // 回滚失败的更新
            failedStudents.forEach(({ id, student, oldCount }) => {
                student.medal_count = oldCount;
                const medalElement = document.querySelector(`#medal-data tr[data-student-id="${id}"] .medal-count`);
                if (medalElement) {
                    medalElement.textContent = oldCount;
                    medalElement.style.backgroundColor = '#ffe8e8';
                    setTimeout(() => {
                        medalElement.style.backgroundColor = '';
                    }, 500);
                }
            });

            // 重新更新排行榜
            this.updateRanking();

            // 显示结果提示
            if (successCount > 0 && failCount > 0) {
                this.showToast(`${successCount}名成功，${failCount}名失败`, 'error');
            } else if (successCount > 0) {
                this.showToast(`${successCount}名学生 ${direction > 0 ? '+' : ''}${changeAmount}🏅`, 'success');
            } else {
                this.showToast('批量更新失败', 'error');
            }
        }
    },
    
    /**
     * 更新筛选器选项
     */
    updateFilters: function() {
        // 更新学校筛选器
        this.updateSchoolFilter();

        // 初始化年级和班级筛选器为禁用状态
        this.initializeFilterStates();

        // 移除模板下载按钮（如果存在）
        const templateBtn = document.getElementById('medal-template');
        if (templateBtn) {
            templateBtn.remove();
        }
    },

    /**
     * 初始化筛选器状态
     */
    initializeFilterStates: function() {
        const gradeFilter = document.getElementById('medal-grade-filter');
        const classFilter = document.getElementById('medal-class-filter');

        // 初始状态：年级筛选器禁用，显示提示
        gradeFilter.innerHTML = '<option value="">请先选择学校</option>';
        gradeFilter.disabled = true;

        // 初始状态：班级筛选器禁用，显示提示
        classFilter.innerHTML = '<option value="">请先选择年级</option>';
        classFilter.disabled = true;
    },

    /**
     * 更新学校筛选器
     */
    updateSchoolFilter: function() {
        const schoolFilter = document.getElementById('medal-school-filter');

        // 创建学校映射，确保去重
        const schoolMap = new Map();
        this.data.forEach(item => {
            if (item.school_id && !schoolMap.has(item.school_id)) {
                schoolMap.set(item.school_id, {
                    id: item.school_id,
                    name: item.school_name || '未知学校'
                });
            }
        });

        // 转换为数组并排序
        const schools = Array.from(schoolMap.values()).sort((a, b) => a.name.localeCompare(b.name));

        // 清空现有选项
        schoolFilter.innerHTML = '<option value="">全部学校</option>';

        // 添加学校选项
        schools.forEach(school => {
            const option = document.createElement('option');
            option.value = school.id;
            option.textContent = school.name;
            schoolFilter.appendChild(option);
        });
    },

    /**
     * 更新年级筛选器
     */
    updateGradeFilter: function() {
        const gradeFilter = document.getElementById('medal-grade-filter');

        if (!this.filter.school_id) {
            // 如果没有选择学校，禁用年级筛选器
            gradeFilter.innerHTML = '<option value="">请先选择学校</option>';
            gradeFilter.disabled = true;
            return;
        }

        // 启用年级筛选器
        gradeFilter.disabled = false;

        // 按选中的学校过滤数据
        const filteredData = this.data.filter(item => item.school_id == this.filter.school_id);
        const { grades } = Utils.getGradesAndClasses(filteredData);

        // 更新年级选项
        Utils.updateSelectOptions(gradeFilter, grades, '全部年级');

        // 重置年级筛选器的值
        gradeFilter.value = this.filter.grade || '';
    },
    
    /**
     * 根据选中的学校和年级更新班级筛选器
     */
    updateClassFilter: function() {
        const classFilter = document.getElementById('medal-class-filter');

        if (!this.filter.school_id) {
            // 如果没有选择学校，禁用班级筛选器
            classFilter.innerHTML = '<option value="">请先选择学校</option>';
            classFilter.disabled = true;
            return;
        }

        if (!this.filter.grade) {
            // 如果没有选择年级，禁用班级筛选器
            classFilter.innerHTML = '<option value="">请先选择年级</option>';
            classFilter.disabled = true;
            return;
        }

        // 启用班级筛选器
        classFilter.disabled = false;

        // 按选中的学校过滤数据
        const filteredData = this.data.filter(item => item.school_id == this.filter.school_id);

        // 获取选中年级下的所有班级
        const { classesByGrade } = Utils.getGradesAndClasses(filteredData);
        const classes = classesByGrade[this.filter.grade] || [];

        // 更新班级选项
        Utils.updateSelectOptions(classFilter, classes, '全部班级');

        // 重置班级筛选器的值
        classFilter.value = this.filter.class || '';
    },

    /**
     * 重置筛选器
     */
    resetFilters: function() {
        // 重置筛选条件对象
        this.filter.school_id = '';
        this.filter.grade = '';
        this.filter.class = '';
        this.filter.search = '';

        // 重置UI上的筛选控件
        document.getElementById('medal-school-filter').value = '';
        document.getElementById('medal-search').value = '';

        // 重置筛选器状态
        this.initializeFilterStates();

        // 重新渲染数据
        this.renderData();

        // 显示提示信息
        Utils.showMessage('筛选条件已重置');
    },
    
    /**
     * 根据筛选条件过滤数据
     * @returns {Array} - 过滤后的数据
     */
    getFilteredData: function() {
        // 调试信息：显示当前筛选条件
        console.log('筛选条件:', JSON.stringify(this.filter));
        
        return this.data.filter(item => {
            // 学校筛选
            if (this.filter.school_id && String(item.school_id) !== String(this.filter.school_id)) {
                return false;
            }

            // 年级筛选 - 优先比较显示用的中文年级
            if (this.filter.grade && this.filter.grade !== '') {
                // 如果item有grade_display属性，先比较它
                if (item.grade_display && item.grade_display !== this.filter.grade) {
                    return false;
                }
                // 如果没有grade_display或不匹配，比较数字年级
                else if (!item.grade_display) {
                    const gradeText = Utils.numberToGrade(item.grade);
                    if (gradeText !== this.filter.grade) {
                        return false;
                    }
                }
            }

            // 班级筛选 - 将两边都转为字符串进行比较
            if (this.filter.class && String(item.class) !== String(this.filter.class)) {
                return false;
            }

            // 搜索筛选
            if (this.filter.search && !String(item.name).toLowerCase().includes(this.filter.search.toLowerCase())) {
                return false;
            }

            return true;
        });
    },
    
    /**
     * 获取排名类样式
     * @param {number} rank - 排名
     * @returns {string} - 样式类名
     */
    getRankClass: function(rank) {
        switch(rank) {
            case 0: return 'rank-1';
            case 1: return 'rank-2';
            case 2: return 'rank-3';
            default: return '';
        }
    },
    
    /**
     * 渲染奖章数据表格
     */
    renderData: function() {
        const filteredData = this.getFilteredData();
        const tbody = document.getElementById('medal-data');
        
        if (filteredData.length === 0) {
            tbody.innerHTML = `<tr><td colspan="7" class="no-data">没有找到相关数据</td></tr>`;
            return;
        }

        // 为确保调试信息可见，先在控制台打印一些关键数据
        console.log('渲染的数据条数:', filteredData.length);
        console.log('第一条数据示例:', filteredData[0]);

        tbody.innerHTML = filteredData.map(item => {
            // 确保ID使用student_identifier
            const id = item.student_identifier;
            if (!id) {
                console.error('警告: 学生数据缺少ID', item);
                return '';
            }

            const isChecked = this.selectedStudents.has(id) ? 'checked' : '';
            return `
                <tr data-student-id="${id}">
                    <td><input type="checkbox" class="student-checkbox" data-id="${id}" ${isChecked}></td>
                    <td>${item.school_name || '未知学校'}</td>
                    <td>${item.grade_display || Utils.numberToGrade(item.grade)}</td>
                    <td>${item.class}</td>
                    <td>${item.name}</td>
                    <td class="medal-count">${item.medal_count || 0}</td>
                    <td>
                        <button class="action-btn increase-btn" data-id="${id}" data-action="increase">+</button>
                        <button class="action-btn decrease-btn" data-id="${id}" data-action="decrease">-</button>
                    </td>
                </tr>
            `;
        }).join('');
        
        this.updateSelectAllCheckbox();
        
        // 添加事件监听器，确保复选框变更时立即更新selectedStudents集合
        const checkboxes = document.querySelectorAll('#medal-data .student-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const id = e.target.getAttribute('data-id');
                if (e.target.checked) {
                    this.selectedStudents.add(id);
                } else {
                    this.selectedStudents.delete(id);
                }
                // 每次点击后在控制台输出当前选中的ID
                console.log('当前选中ID:', [...this.selectedStudents]);
            });
        });
    },
    
    /**
     * 更新奖章数量
     * @param {string} id - 学生ID
     * @param {number} change - 变化量（正数为增加，负数为减少）
     */
    updateMedalCount: async function(id, change) {
        // 查找学生
        const student = this.data.find(item => item.student_identifier === id);
        if (!student) {
            console.error('未找到学生:', id);
            return;
        }

        // 计算新的奖章数量（不低于0）
        const newCount = Math.max(0, student.medal_count + change);

        // 立即更新UI（乐观更新）
        const medalElement = document.querySelector(`#medal-data tr[data-student-id="${id}"] .medal-count`);
        if (medalElement) {
            medalElement.textContent = newCount;
            // 添加视觉反馈
            medalElement.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                medalElement.style.backgroundColor = '';
            }, 300);
        }

        // 立即更新本地数据
        const oldCount = student.medal_count;
        student.medal_count = newCount;

        // 立即更新排行榜
        this.updateRanking();

        // 异步发送到服务器（静默更新）
        try {
            await DB.updateMedals(id, newCount);

            // 成功时显示简洁的右上角提示
            this.showToast(`${student.name} ${change > 0 ? '+' : ''}${change}🏅`, 'success');
        } catch (error) {
            console.error('更新奖章数量失败:', error);

            // 失败时回滚UI
            student.medal_count = oldCount;
            if (medalElement) {
                medalElement.textContent = oldCount;
                medalElement.style.backgroundColor = '#ffe8e8';
                setTimeout(() => {
                    medalElement.style.backgroundColor = '';
                }, 500);
            }
            this.updateRanking();

            // 显示错误提示
            this.showToast(`${student.name} 更新失败`, 'error');
        }
    },

    /**
     * 显示右上角Toast提示
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型 ('success', 'error', 'info')
     */
    showToast: function(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 设置样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease',
            maxWidth: '300px',
            wordBreak: 'break-word'
        });

        // 根据类型设置背景色
        switch (type) {
            case 'success':
                toast.style.backgroundColor = '#10b981';
                break;
            case 'error':
                toast.style.backgroundColor = '#ef4444';
                break;
            default:
                toast.style.backgroundColor = '#3b82f6';
        }

        // 添加到页面
        document.body.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 2000);
    },

    /**
     * 更新排行榜
     */
    updateRanking: function() {
        // 清除旧图表
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
        
        switch (this.rankingType) {
            case 'individual':
                this.updateIndividualRanking();
                break;
            case 'class':
                this.updateClassRanking();
                break;
            case 'grade':
                this.updateGradeRanking();
                break;
            default:
                this.updateIndividualRanking();
        }
    },
    
    /**
     * 更新个人排行榜
     */
    updateIndividualRanking: function() {
        const rankingDiv = document.getElementById('medal-ranking');
        
        // 按年级分组并按奖章数量从高到低排序
        const gradeGroups = {};
        
        // 首先按年级对学生进行分组（使用grade_display或转换后的中文年级）
        this.data.forEach(student => {
            const gradeDisplay = student.grade_display || Utils.numberToGrade(student.grade);
            if (!gradeGroups[gradeDisplay]) {
                gradeGroups[gradeDisplay] = [];
            }
            gradeGroups[gradeDisplay].push(student);
        });
        
        // 对每个年级组内的学生按奖章数量排序
        for (const grade in gradeGroups) {
            gradeGroups[grade].sort((a, b) => b.medal_count - a.medal_count);
        }
        
        // 如果没有数据，显示提示信息
        if (Object.keys(gradeGroups).length === 0) {
            rankingDiv.innerHTML = '<div class="no-data">暂无排名数据</div>';
            this.createChart({
                labels: [],
                data: [],
                title: '个人奖章排行'
            });
            return;
        }
        
        // 获取排行榜中要显示的年级，优先使用筛选器中选择的年级
        let selectedGrade = this.filter.grade && gradeGroups[this.filter.grade] 
            ? this.filter.grade 
            : Object.keys(gradeGroups)[0];
        
        // 添加年级选择器到顶部
        let html = `
            <div class="grade-selector">
                <label>选择年级：</label>
                <select id="medal-ranking-grade-selector">
                    ${Object.keys(gradeGroups).map(grade => 
                        `<option value="${grade}" ${grade === selectedGrade ? 'selected' : ''}>${grade}</option>`
                    ).join('')}
                </select>
            </div>
        `;
        
        // 构建所选年级的排行榜HTML
        const topStudents = gradeGroups[selectedGrade].slice(0, 10);
        const chartLabels = [];
        const chartData = [];
        
        // 如果该年级有学生数据，显示该年级的排行榜
        if (topStudents.length > 0) {
            // 添加排行榜容器
            html += `
                <div class="ranking-section" id="medal-grade-ranking">
                    <h4>${selectedGrade}排行榜 (前10名)</h4>
                    ${topStudents.map((student, index) => {
                        // 只取前三名的数据添加到图表中
                        if (index < 3) {
                            chartLabels.push(`${student.class} ${student.name}`);
                            chartData.push(student.medal_count);
                        }
                        
                        return `
                            <div class="ranking-item ${this.getRankClass(index)}">
                                <div>
                                    <span class="rank">&nbsp;</span>
                                    <span>${student.class} ${student.name}</span>
                                </div>
                                <div>${student.medal_count}枚</div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        } else {
            html += `<div class="no-data" id="medal-grade-ranking">该年级暂无排名数据</div>`;
        }
        
        // 显示排行榜
        rankingDiv.innerHTML = html;
        
        // 图表形式展示（只显示所选年级前三名）
        this.createChart({
            labels: chartLabels,
            data: chartData,
            title: `${selectedGrade}奖章排行榜(前三名)`
        });
        
        // 绑定年级选择器的变更事件，直接使用闭包避免重复绑定事件
        document.getElementById('medal-ranking-grade-selector').addEventListener('change', (e) => {
            const newSelectedGrade = e.target.value;
            
            // 切换排行榜时重用gradeGroups数据，不需要重新计算
            this.updateGradeRankingContent(newSelectedGrade, gradeGroups);
        });
    },
    
    /**
     * 更新年级排行榜内容，复用已有分组数据
     * @param {string} selectedGrade - 选中的年级
     * @param {Object} gradeGroups - 按年级分组的学生数据
     */
    updateGradeRankingContent: function(selectedGrade, gradeGroups) {
        const topStudents = gradeGroups[selectedGrade].slice(0, 10);
        const chartLabels = [];
        const chartData = [];
        
        // 构建新的排行榜HTML
        let rankingHTML = '';
        
        if (topStudents.length > 0) {
            rankingHTML = `
                <h4>${selectedGrade}排行榜 (前10名)</h4>
                ${topStudents.map((student, index) => {
                    // 只取前三名的数据添加到图表中
                    if (index < 3) {
                        chartLabels.push(`${student.class} ${student.name}`);
                        chartData.push(student.medal_count);
                    }
                    
                    return `
                        <div class="ranking-item ${this.getRankClass(index)}">
                            <div>
                                <span class="rank">&nbsp;</span>
                                <span>${student.class} ${student.name}</span>
                            </div>
                            <div>${student.medal_count}枚</div>
                        </div>
                    `;
                }).join('')}
            `;
        } else {
            rankingHTML = `<div class="no-data">该年级暂无排名数据</div>`;
        }
        
        // 更新排行榜内容
        const rankingSection = document.getElementById('medal-grade-ranking');
        if (rankingSection) {
            rankingSection.innerHTML = rankingHTML;
        }
        
        // 更新图表
        this.createChart({
            labels: chartLabels,
            data: chartData,
            title: `${selectedGrade}奖章排行榜(前三名)`
        });
        
        // 保持下拉框选中状态
        document.getElementById('medal-ranking-grade-selector').value = selectedGrade;
    },
    
    /**
     * 更新班级排行榜
     */
    updateClassRanking: function() {
        const rankingDiv = document.getElementById('medal-ranking');
        
        // 按班级分组
        const classGroups = {};
        
        // 首先按班级对学生进行分组（格式：年级-班级），使用grade_display或转换后的中文年级
        this.data.forEach(student => {
            const gradeDisplay = student.grade_display || Utils.numberToGrade(student.grade);
            const classKey = `${gradeDisplay}-${student.class}`;
            if (!classGroups[classKey]) {
                classGroups[classKey] = [];
            }
            classGroups[classKey].push(student);
        });
        
        // 如果没有数据，显示提示信息
        if (Object.keys(classGroups).length === 0) {
            rankingDiv.innerHTML = '<div class="no-data">暂无班级排名数据</div>';
            return;
        }
        
        // 构建班级内学生排行榜HTML
        let html = '';
        let chartLabels = [];
        let chartData = [];
        
        // 获取筛选器中选择的班级
        const selectedGrade = this.filter.grade;
        const selectedClass = this.filter.class;
        
        // 确定要显示哪个班级的排行
        let classToShow = '';
        
        if (selectedGrade && selectedClass) {
            // 如果有选择年级和班级，则显示所选班级的排行
            classToShow = `${selectedGrade}-${selectedClass}`;
        } else {
            // 否则显示第一个班级
            classToShow = Object.keys(classGroups)[0];
        }
        
        let grade = '';
        let className = '';
        
        // 对选中班级的学生按奖章数量排序
        if (classGroups[classToShow]) {
            classGroups[classToShow].sort((a, b) => b.medal_count - a.medal_count);
            
            // 解析班级信息
            [grade, className] = classToShow.split('-');
            
            // 构建班级排行HTML
            html += `
                <div class="ranking-section">
                    <h4>${grade} ${className}班排行</h4>
                    ${classGroups[classToShow].map((student, index) => {
                        // 只取前三名的数据添加到图表中
                        if (index < 3) {
                            chartLabels.push(`${student.name}`);
                            chartData.push(student.medal_count);
                        }
                        
                        return `
                            <div class="ranking-item ${this.getRankClass(index)}">
                                <div>
                                    <span class="rank">&nbsp;</span>
                                    <span>${student.name}</span>
                                </div>
                                <div>${student.medal_count}枚</div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        }
        
        // 添加班级选择器
        html = `
            <div class="class-selector">
                <label>选择班级：</label>
                <select id="ranking-class-selector">
                    ${Object.keys(classGroups).map(key => {
                        const [g, c] = key.split('-');
                        return `<option value="${key}" ${key === classToShow ? 'selected' : ''}>${g} ${c}班</option>`;
                    }).join('')}
                </select>
            </div>
        ` + html;
        
        // 显示排行榜
        rankingDiv.innerHTML = html;
        
        // 图表形式展示（班级内前三名）
        if (chartLabels.length > 0 && chartData.length > 0) {
            this.createChart({
                labels: chartLabels,
                data: chartData,
                title: `${grade} ${className}班 奖章排行(前三名)`
            });
        }
        
        // 绑定班级选择器的变更事件
        document.getElementById('ranking-class-selector').addEventListener('change', (e) => {
            const selectedClassKey = e.target.value;
            
            // 按选中的班级重新显示排行榜
            if (classGroups[selectedClassKey]) {
                // 对选中班级的学生按奖章数量排序
                classGroups[selectedClassKey].sort((a, b) => b.medal_count - a.medal_count);
                
                // 解析班级信息
                const [grade, className] = selectedClassKey.split('-');
                
                // 重新构建图表数据（清空之前的数据）
                const newChartLabels = [];
                const newChartData = [];
                
                // 构建班级排行HTML
                let newHtml = `
                    <div class="ranking-section">
                        <h4>${grade} ${className}班排行</h4>
                        ${classGroups[selectedClassKey].map((student, index) => {
                            // 只取前三名的数据添加到图表中
                            if (index < 3) {
                                newChartLabels.push(student.name);
                                newChartData.push(student.medal_count);
                            }
                            
                            return `
                                <div class="ranking-item ${this.getRankClass(index)}">
                                    <div>
                                        <span class="rank">&nbsp;</span>
                                        <span>${student.name}</span>
                                    </div>
                                    <div>${student.medal_count}枚</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                `;
                
                // 更新图表
                if (newChartLabels.length > 0 && newChartData.length > 0) {
                    // 清除旧图表
                    if (this.chart) {
                        this.chart.destroy();
                        this.chart = null;
                    }
                    
                    this.createChart({
                        labels: newChartLabels,
                        data: newChartData,
                        title: `${grade} ${className}班 奖章排行(前三名)`
                    });
                }
                
                // 保留选择器部分
                newHtml = rankingDiv.querySelector('.class-selector').outerHTML + newHtml;
                
                // 更新排行榜HTML
                rankingDiv.innerHTML = newHtml;
                
                // 重新绑定事件（使用一个新的函数）
                const rankingClassSelector = document.getElementById('ranking-class-selector');
                rankingClassSelector.value = selectedClassKey;
                rankingClassSelector.addEventListener('change', () => {
                    // 直接重新调用整个方法，完全刷新排行榜
                    this.rankingType = 'class';
                    this.updateRanking();
                });
            }
        });
    },
    
    /**
     * 更新年级排行榜
     */
    updateGradeRanking: function() {
        const rankingDiv = document.getElementById('medal-ranking');
        
        // 按班级统计奖章总数
        const classTotalMedals = {};
        
        // 计算每个班级的奖章总数，使用grade_display或转换后的中文年级
        this.data.forEach(student => {
            const gradeDisplay = student.grade_display || Utils.numberToGrade(student.grade);
            const classKey = `${gradeDisplay}-${student.class}`;
            if (!classTotalMedals[classKey]) {
                classTotalMedals[classKey] = {
                    grade: gradeDisplay, // 使用中文年级
                    class: student.class,
                    totalMedals: 0,
                    studentCount: 0
                };
            }
            classTotalMedals[classKey].totalMedals += student.medal_count;
            classTotalMedals[classKey].studentCount++;
        });
        
        // 将班级数据转换为数组并按奖章总数排序
        const classRanking = Object.values(classTotalMedals).sort((a, b) => b.totalMedals - a.totalMedals);
        
        if (classRanking.length === 0) {
            rankingDiv.innerHTML = '<div class="no-data">暂无年级排名数据</div>';
            this.createChart({
                labels: [],
                data: [],
                title: '班级奖章总数排行'
            });
            return;
        }
        
        // 按年级对班级进行分组
        const gradeGroups = {};
        classRanking.forEach(classData => {
            if (!gradeGroups[classData.grade]) {
                gradeGroups[classData.grade] = [];
            }
            gradeGroups[classData.grade].push(classData);
        });
        
        // 获取排行榜中要显示的年级，优先使用筛选器中选择的年级
        let selectedGrade = this.filter.grade && gradeGroups[this.filter.grade] 
            ? this.filter.grade 
            : Object.keys(gradeGroups)[0];
        
        // 添加年级选择器到顶部
        let html = `
            <div class="grade-selector">
                <label>选择年级：</label>
                <select id="medal-grade-class-selector">
                    ${Object.keys(gradeGroups).map(grade => 
                        `<option value="${grade}" ${grade === selectedGrade ? 'selected' : ''}>${grade}</option>`
                    ).join('')}
                </select>
            </div>
        `;
        
        // 构建所选年级的班级排行榜HTML
        html += this.renderGradeClassesRanking(selectedGrade, gradeGroups);
        
        // 显示排行榜
        rankingDiv.innerHTML = html;
        
        // 更新图表（只显示选中年级前三名班级）
        this.updateGradeClassesChart(selectedGrade, gradeGroups);
        
        // 绑定年级选择器的变更事件
        document.getElementById('medal-grade-class-selector').addEventListener('change', (e) => {
            const newSelectedGrade = e.target.value;
            
            // 更新排行榜HTML
            const rankingSection = document.getElementById('medal-grade-classes-ranking');
            if (rankingSection) {
                rankingSection.innerHTML = this.renderGradeClassesRanking(newSelectedGrade, gradeGroups, false);
            }
            
            // 更新图表
            this.updateGradeClassesChart(newSelectedGrade, gradeGroups);
            
            // 保持下拉框选中状态
            document.getElementById('medal-grade-class-selector').value = newSelectedGrade;
        });
    },
    
    /**
     * 渲染特定年级下的班级排行榜HTML
     * @param {string} grade - 年级
     * @param {Object} gradeGroups - 按年级分组的班级数据
     * @param {boolean} withContainer - 是否包含容器div（默认true）
     * @returns {string} - 生成的HTML
     */
    renderGradeClassesRanking: function(grade, gradeGroups, withContainer = true) {
        // 获取该年级的班级排行
        const gradeClasses = gradeGroups[grade] || [];
        
        // 构建排行榜HTML
        let html = '';
        
        if (gradeClasses.length > 0) {
            if (withContainer) {
                html = `<div class="ranking-section" id="medal-grade-classes-ranking">`;
            }
            
            html += `
                <h4>${grade}各班级奖章排行</h4>
                ${gradeClasses.map((classData, index) => {
                    return `
                        <div class="ranking-item ${this.getRankClass(index)}">
                            <div>
                                <span class="rank">&nbsp;</span>
                                <span>${classData.class}班</span>
                            </div>
                            <div>${classData.totalMedals}枚 (${classData.studentCount}人)</div>
                        </div>
                    `;
                }).join('')}
            `;
            
            if (withContainer) {
                html += `</div>`;
            }
        } else {
            html = `<div class="no-data" id="medal-grade-classes-ranking">该年级暂无班级排名数据</div>`;
        }
        
        return html;
    },
    
    /**
     * 更新年级内班级排行图表
     * @param {string} grade - 年级
     * @param {Object} gradeGroups - 按年级分组的班级数据
     */
    updateGradeClassesChart: function(grade, gradeGroups) {
        const gradeClasses = gradeGroups[grade] || [];
        const chartLabels = [];
        const chartData = [];
        
        // 获取前三名班级数据
        gradeClasses.slice(0, 3).forEach(classData => {
            chartLabels.push(`${grade} ${classData.class}班`);
            chartData.push(classData.totalMedals);
        });
        
        // 更新图表
        this.createChart({
            labels: chartLabels,
            data: chartData,
            title: `${grade}班级奖章总数排行(前三名)`
        });
    },
    
    /**
     * 创建图表
     * @param {Object} chartData - 图表数据对象
     */
    createChart: function(chartData) {
        const ctx = document.getElementById('medal-chart').getContext('2d');
        
        // 如果已有图表实例，销毁它
        if (this.chart) {
            this.chart.destroy();
        }
        
        // 随机生成背景颜色
        const backgroundColors = chartData.labels.map((_, index) => {
            return CONFIG.COLORS[index % CONFIG.COLORS.length];
        });
        
        this.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: '奖章数量',
                    data: chartData.data,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors.map(color => color),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1000, // 动画持续时间（毫秒）
                    easing: 'easeOutQuart' // 缓动函数
                },
                plugins: {
                    title: {
                        display: true,
                        text: chartData.title,
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `奖章数量: ${context.parsed.y}枚`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '奖章数量'
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    },
    
    /**
     * 导出数据为Excel
     */
    handleExport: function() {
        Utils.showLoading(true);
        
        setTimeout(() => {
            try {
                // 获取当前筛选后的数据
                const filteredData = this.getFilteredData();
                
                // 转换为Excel友好格式
                const excelData = filteredData.map(item => ({
                    '年级': item.grade_display || Utils.numberToGrade(item.grade),
                    '班级': item.class,
                    '姓名': item.name,
                    '奖章数量': item.medal_count
                }));
                
                // 导出Excel
                Utils.exportToExcel(excelData, '奖章数据');
                
                Utils.showMessage('奖章数据导出成功');
            } catch (error) {
                console.error('导出奖章数据失败:', error);
                Utils.showMessage('导出失败，请重试');
            } finally {
                Utils.showLoading(false);
            }
        }, 300);
    },
    
    /**
     * 导入奖章数据
     */
    handleImport: async function() {
        try {
            // 创建一个文件输入元素
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.xlsx, .xls';
            
            // 监听文件选择
            fileInput.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                Utils.showLoading(true);
                Utils.showMessage('正在导入数据，请稍候...');
                
                try {
                    // 读取Excel文件
                    const reader = new FileReader();
                    
                    reader.onload = async (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const sheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[sheetName];
                            const jsonData = XLSX.utils.sheet_to_json(worksheet);
                            
                            let successCount = 0;
                            let failCount = 0;
                            
                            // 处理每一行数据
                            for (const row of jsonData) {
                                try {
                                    // 调试信息：打印当前处理的行
                                    console.log('正在处理行:', row);
                                    
                                    // 验证必要字段
                                    if (!row['年级']) {
                                        console.warn('缺少年级字段:', row);
                                        failCount++;
                                        continue;
                                    }
                                    if (!row['班级']) {
                                        console.warn('缺少班级字段:', row);
                                        failCount++;
                                        continue;
                                    }
                                    if (!row['姓名']) {
                                        console.warn('缺少姓名字段:', row);
                                        failCount++;
                                        continue;
                                    }
                                    if (row['奖章数量'] === undefined) {
                                        console.warn('缺少奖章数量字段:', row);
                                        failCount++;
                                        continue;
                                    }
                                    
                                    // 获取年级文本和转换为数字
                                    const gradeText = row['年级'];
                                    console.log('年级文本:', gradeText);
                                    
                                    // 尝试转换年级
                                    const gradeNumber = Utils.gradeToNumber(gradeText);
                                    console.log('转换后的数字年级:', gradeNumber);
                                    
                                    // 获取显示用的中文年级（完整格式）
                                    const gradeDisplay = Utils.numberToGrade(gradeNumber);
                                    
                                    // 学生数据 (使用数字年级)
                                    const studentData = {
                                        grade: gradeNumber, // 数字年级
                                        class: row['班级'],
                                        name: row['姓名']
                                    };
                                    
                                    // 尝试创建学生（如果学生已存在，API会处理）
                                    await DB.createStudent(studentData);
                                    
                                    // 构造与服务器相同的学生ID格式 (使用数字年级)
                                    const studentId = `${gradeNumber}_${row['班级']}_${row['姓名']}`;
                                    console.log('构造的学生ID:', studentId);
                                    
                                    // 设置奖章数量
                                    await DB.updateMedals(studentId, parseInt(row['奖章数量']) || 0);
                                    
                                    successCount++;
                                } catch (error) {
                                    console.error('导入行数据失败:', error.message, row);
                                    failCount++;
                                }
                            }
                            
                            // 重新加载数据
                            await this.loadData();
                            
                            Utils.showMessage(`导入完成。成功: ${successCount}，失败: ${failCount}`);
                        } catch (error) {
                            console.error('处理Excel数据失败:', error);
                            Utils.showMessage('导入失败: ' + error.message, 'error');
                        } finally {
                            Utils.showLoading(false);
                        }
                    };
                    
                    reader.onerror = (error) => {
                        console.error('读取文件失败:', error);
                        Utils.showMessage('读取文件失败', 'error');
                        Utils.showLoading(false);
                    };
                    
                    reader.readAsArrayBuffer(file);
                } catch (error) {
                    console.error('导入奖章数据失败:', error);
                    Utils.showMessage('导入失败，请重试');
                    Utils.showLoading(false);
                }
            };
            
            // 触发文件选择对话框
            fileInput.click();
        } catch (error) {
            console.error('启动导入过程失败:', error);
            Utils.showMessage('导入功能初始化失败，请重试');
        }
    },
    
    /**
     * 显示添加学生对话框
     */
    showAddStudentModal: function() {
        const modal = document.getElementById('add-student-modal');
        modal.style.display = 'block';
        
        // 重置表单
        document.getElementById('add-student-grade').value = '一年级';
        document.getElementById('add-student-class').value = '';
        document.getElementById('add-student-name').value = '';
        document.getElementById('add-student-medals').value = '0';
    },
    
    /**
     * 关闭添加学生对话框
     */
    closeAddStudentModal: function() {
        const modal = document.getElementById('add-student-modal');
        modal.style.display = 'none';
    },
    
    /**
     * 保存新添加的学生
     */
    saveNewStudent: async function() {
        const gradeText = document.getElementById('add-student-grade').value;
        const classValue = document.getElementById('add-student-class').value;
        const name = document.getElementById('add-student-name').value;
        const medals = parseInt(document.getElementById('add-student-medals').value) || 0;
        
        // 验证输入
        if (!gradeText || !classValue || !name) {
            Utils.showMessage('请填写所有必填字段', 'error');
            return;
        }
        
        // 显示加载状态
        Utils.showLoading(true);
        
        try {
            // 将中文年级转为数字
            const grade = Utils.gradeToNumber(gradeText);
            
            // 构造学生数据
            const studentData = {
                grade: grade, // 数字年级
                class: classValue,
                name: name
            };
            
            // 首先创建学生
            await DB.createStudent(studentData);
            
            // 构造与服务器相同的学生ID格式 (使用数字年级)
            const studentId = `${grade}_${classValue}_${name}`;
            
            // 然后设置奖章数量
            if (medals > 0) {
                await DB.updateMedals(studentId, medals);
            }
            
            // 关闭对话框
            this.closeAddStudentModal();
            
            // 重新加载数据
            await this.loadData();
            
            Utils.showMessage(`已成功添加学生: ${name}，奖章数量: ${medals}`);
        } catch (error) {
            console.error('添加学生失败:', error);
            Utils.showMessage('添加学生失败: ' + error.message, 'error');
        } finally {
            Utils.showLoading(false);
        }
    },

    /**
     * 运行数据库诊断（调试用）
     */
    runDiagnostic: async function() {
        try {
            Utils.showLoading(true);
            console.log('开始数据库诊断...');

            const response = await fetch('/api/db-diagnostic', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('数据库诊断结果:', result);

            // 显示诊断结果
            let message = '数据库诊断结果:\n\n';
            for (const [tableName, info] of Object.entries(result.diagnostic.tables)) {
                message += `${tableName}表: ${info.exists ? '存在' : '不存在'}\n`;
                message += `  记录数: ${info.count}\n`;
                if (info.error) {
                    message += `  错误: ${info.error}\n`;
                }
                message += '\n';
            }

            alert(message);
            Utils.showMessage('诊断完成，请查看控制台详细信息');
        } catch (error) {
            console.error('诊断失败:', error);
            Utils.showMessage('诊断失败: ' + error.message, 'error');
        } finally {
            Utils.showLoading(false);
        }
    }
};