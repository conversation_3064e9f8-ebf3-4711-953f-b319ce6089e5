/**
 * 学校层级管理系统
 * 实现学校 > 年级 > 班级的层级管理
 */

// 学校管理相关全局变量
let currentSchool = null;
let schoolGradeConfigs = {};

/**
 * 显示学校管理界面（增强版）
 */
async function showEnhancedSchoolManagement() {
    const contentArea = document.getElementById('admin-content-area');
    
    // 加载学校数据
    const schools = await loadAllSchools();
    
    contentArea.innerHTML = `
        <div class="management-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
            <div>
                <h2 style="color: #333; margin: 0;">
                    <i class="fas fa-school"></i> 学校信息管理
                </h2>
                <p style="color: #666; margin: 5px 0 0 0;">管理学校基本信息和年级班级配置</p>
            </div>
            <div>
                <button onclick="showAddSchoolModal()" class="btn btn-primary me-2">
                    <i class="fas fa-plus"></i> 添加学校
                </button>
                <button onclick="showSchoolConfigManager()" class="btn btn-info">
                    <i class="fas fa-cog"></i> 批量配置
                </button>
            </div>
        </div>

        <div class="row">
            ${schools.map(school => generateSchoolCard(school)).join('')}
        </div>

        <!-- 如果没有学校 -->
        ${schools.length === 0 ? `
            <div class="text-center py-5">
                <i class="fas fa-school fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无学校信息</h4>
                <p class="text-muted">点击上方"添加学校"按钮开始添加学校信息</p>
            </div>
        ` : ''}

        <!-- 学校配置模态框 -->
        <div class="modal fade" id="schoolConfigModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">配置学校年级班级</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="schoolConfigContent">
                            <!-- 动态内容 -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveSchoolConfig()">保存配置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量配置模态框 -->
        <div class="modal fade" id="batchConfigModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量配置学校年级班级</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="batchConfigContent">
                            <!-- 动态内容 -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveBatchConfig()">批量保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 加载所有学校数据
 */
async function loadAllSchools() {
    try {
        const response = await fetch('/api/admin/schools', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            return result.data || [];
        }
    } catch (error) {
        console.error('加载学校数据失败:', error);
    }
    return [];
}

/**
 * 生成学校卡片
 */
function generateSchoolCard(school) {
    return `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card border-primary h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-school me-2"></i>${school.name}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">创建时间</small><br>
                        <span>${formatDateTime(school.created_at)}</span>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">年级配置</small><br>
                        <div id="school-grades-${school.id}" class="mt-1">
                            <span class="badge bg-secondary">加载中...</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <button class="btn btn-sm btn-outline-primary" onclick="configureSchoolGrades(${school.id}, '${school.name}')">
                            <i class="fas fa-cog"></i> 配置
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="showEditSchoolModal(${school.id}, '${school.name}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteSchool(${school.id}, '${school.name}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 配置学校年级班级
 */
async function configureSchoolGrades(schoolId, schoolName) {
    currentSchool = { id: schoolId, name: schoolName };
    
    // 获取当前配置
    const config = await getSchoolGradeConfig(schoolId);
    
    document.getElementById('schoolConfigContent').innerHTML = `
        <h6 class="mb-3">${schoolName} - 年级班级配置</h6>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            配置该学校包含哪些年级，每个年级有多少个班级
        </div>
        
        <div id="gradeConfigContainer">
            ${generateGradeConfigForm(config)}
        </div>
        
        <div class="mt-3">
            <button type="button" class="btn btn-sm btn-success" onclick="addGradeConfig()">
                <i class="fas fa-plus"></i> 添加年级
            </button>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('schoolConfigModal'));
    modal.show();
}

/**
 * 获取学校年级配置
 */
async function getSchoolGradeConfig(schoolId) {
    try {
        const response = await fetch(`/api/admin/schools/${schoolId}/config`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            return result.data || [];
        }
    } catch (error) {
        console.error('获取学校配置失败:', error);
    }
    
    // 返回默认配置
    return [
        { grade: 1, class_count: 2 },
        { grade: 2, class_count: 2 },
        { grade: 3, class_count: 3 }
    ];
}

/**
 * 生成年级配置表单
 */
function generateGradeConfigForm(config) {
    return config.map((item, index) => `
        <div class="row mb-3 grade-config-row">
            <div class="col-md-4">
                <label class="form-label">年级</label>
                <select class="form-control grade-select" data-index="${index}">
                    ${Array.from({length: 12}, (_, i) => i + 1).map(grade => 
                        `<option value="${grade}" ${item.grade === grade ? 'selected' : ''}>${grade}年级</option>`
                    ).join('')}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">班级数量</label>
                <input type="number" class="form-control class-count-input" 
                       min="1" max="20" value="${item.class_count}" data-index="${index}">
            </div>
            <div class="col-md-4">
                <label class="form-label">操作</label><br>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeGradeConfig(${index})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');
}

/**
 * 添加年级配置
 */
function addGradeConfig() {
    const container = document.getElementById('gradeConfigContainer');
    const currentRows = container.querySelectorAll('.grade-config-row').length;
    
    const newRow = document.createElement('div');
    newRow.className = 'row mb-3 grade-config-row';
    newRow.innerHTML = `
        <div class="col-md-4">
            <label class="form-label">年级</label>
            <select class="form-control grade-select" data-index="${currentRows}">
                ${Array.from({length: 12}, (_, i) => i + 1).map(grade => 
                    `<option value="${grade}">${grade}年级</option>`
                ).join('')}
            </select>
        </div>
        <div class="col-md-4">
            <label class="form-label">班级数量</label>
            <input type="number" class="form-control class-count-input" 
                   min="1" max="20" value="2" data-index="${currentRows}">
        </div>
        <div class="col-md-4">
            <label class="form-label">操作</label><br>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeGradeConfig(${currentRows})">
                <i class="fas fa-trash"></i> 删除
            </button>
        </div>
    `;
    
    container.appendChild(newRow);
}

/**
 * 删除年级配置
 */
function removeGradeConfig(index) {
    const rows = document.querySelectorAll('.grade-config-row');
    if (rows.length > 1) {  // 至少保留一个年级
        rows[index].remove();
    } else {
        Utils.showMessage('至少需要保留一个年级配置', 'warning');
    }
}

/**
 * 保存学校配置
 */
async function saveSchoolConfig() {
    if (!currentSchool) return;
    
    const gradeSelects = document.querySelectorAll('.grade-select');
    const classCountInputs = document.querySelectorAll('.class-count-input');
    
    const grades = [];
    for (let i = 0; i < gradeSelects.length; i++) {
        grades.push({
            grade: parseInt(gradeSelects[i].value),
            class_count: parseInt(classCountInputs[i].value)
        });
    }
    
    try {
        const response = await fetch(`/api/admin/schools/${currentSchool.id}/config`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({ grades })
        });

        if (response.ok) {
            Utils.showMessage('学校配置保存成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('schoolConfigModal'));
            if (modal) modal.hide();
            // 刷新显示
            await updateSchoolGradesDisplay();
        } else {
            const result = await response.json();
            Utils.showMessage(result.message || '保存失败', 'error');
        }
    } catch (error) {
        console.error('保存学校配置失败:', error);
        Utils.showMessage('保存失败，请重试', 'error');
    }
}

/**
 * 更新学校年级显示
 */
async function updateSchoolGradesDisplay() {
    const schools = await loadAllSchools();
    
    for (const school of schools) {
        const config = await getSchoolGradeConfig(school.id);
        const container = document.getElementById(`school-grades-${school.id}`);
        
        if (container) {
            if (config.length === 0) {
                container.innerHTML = '<span class="badge bg-warning">未配置</span>';
            } else {
                const badges = config.map(c => 
                    `<span class="badge bg-info me-1">${c.grade}年级(${c.class_count}班)</span>`
                ).join('');
                container.innerHTML = badges;
            }
        }
    }
}

/**
 * 显示批量配置管理器
 */
async function showSchoolConfigManager() {
    const schools = await loadAllSchools();
    
    document.getElementById('batchConfigContent').innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            批量为所有学校配置年级班级设置，可以选择应用模板或单独配置
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <h6>快速模板</h6>
                <div class="list-group">
                    <button class="list-group-item list-group-item-action" onclick="applyTemplate('primary')">
                        <strong>小学模板</strong><br>
                        <small>1-6年级，每年级3个班</small>
                    </button>
                    <button class="list-group-item list-group-item-action" onclick="applyTemplate('junior')">
                        <strong>初中模板</strong><br>
                        <small>7-9年级，每年级4个班</small>
                    </button>
                    <button class="list-group-item list-group-item-action" onclick="applyTemplate('senior')">
                        <strong>高中模板</strong><br>
                        <small>10-12年级，每年级6个班</small>
                    </button>
                </div>
            </div>
            <div class="col-md-8">
                <h6>学校配置</h6>
                <div id="batchSchoolConfigs">
                    ${schools.map(school => `
                        <div class="card mb-3">
                            <div class="card-header">
                                <div class="form-check">
                                    <input class="form-check-input school-checkbox" type="checkbox" 
                                           id="school-${school.id}" value="${school.id}">
                                    <label class="form-check-label" for="school-${school.id}">
                                        <strong>${school.name}</strong>
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="small text-muted">当前配置将显示在这里</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('batchConfigModal'));
    modal.show();
}

/**
 * 应用配置模板
 */
function applyTemplate(templateType) {
    const templates = {
        primary: [
            { grade: 1, class_count: 3 },
            { grade: 2, class_count: 3 },
            { grade: 3, class_count: 3 },
            { grade: 4, class_count: 3 },
            { grade: 5, class_count: 3 },
            { grade: 6, class_count: 3 }
        ],
        junior: [
            { grade: 7, class_count: 4 },
            { grade: 8, class_count: 4 },
            { grade: 9, class_count: 4 }
        ],
        senior: [
            { grade: 10, class_count: 6 },
            { grade: 11, class_count: 6 },
            { grade: 12, class_count: 6 }
        ]
    };
    
    const template = templates[templateType];
    if (!template) return;
    
    const selectedSchools = Array.from(document.querySelectorAll('.school-checkbox:checked'))
        .map(cb => parseInt(cb.value));
    
    if (selectedSchools.length === 0) {
        Utils.showMessage('请先选择要应用模板的学校', 'warning');
        return;
    }
    
    // 这里可以预览模板效果
    Utils.showMessage(`将为 ${selectedSchools.length} 个学校应用${templateType}模板`, 'info');
}

// 在页面加载完成后更新学校年级显示
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(updateSchoolGradesDisplay, 1000);
});

// 导出到全局作用域
window.configureSchoolGrades = configureSchoolGrades;
window.saveSchoolConfig = saveSchoolConfig;
window.showSchoolConfigManager = showSchoolConfigManager;
window.addGradeConfig = addGradeConfig;
window.removeGradeConfig = removeGradeConfig;
window.applyTemplate = applyTemplate;