/**
 * 教师管理后台 - 全新版本 2.0
 * 功能：控制台预览、学校管理、年级管理、学生管理
 * 界面风格：与主界面保持一致
 */

// ==================== 全局变量 ====================
let currentUser = null;
let currentSection = 'dashboard';
let schools = [];
let grades = [];
let students = [];

// ==================== 分页管理器 ====================

/**
 * 通用分页管理器类
 */
class PaginationManager {
    constructor(containerId, defaultPageSize = 10) {
        this.containerId = containerId;
        this.currentPage = 1;
        this.pageSize = this.loadPageSize(containerId) || defaultPageSize;
        this.totalItems = 0;
        this.data = [];
        this.filteredData = [];
    }

    /**
     * 从localStorage加载页面大小设置
     */
    loadPageSize(containerId) {
        const saved = localStorage.getItem(`pageSize_${containerId}`);
        return saved ? parseInt(saved) : null;
    }

    /**
     * 保存页面大小设置到localStorage
     */
    savePageSize() {
        localStorage.setItem(`pageSize_${this.containerId}`, this.pageSize);
    }

    /**
     * 设置数据
     */
    setData(data) {
        this.data = data;
        this.filteredData = data;
        this.totalItems = data.length;
        this.currentPage = 1;
        return this;
    }

    /**
     * 设置筛选后的数据
     */
    setFilteredData(filteredData) {
        this.filteredData = filteredData;
        this.totalItems = filteredData.length;
        this.currentPage = 1;
        return this;
    }

    /**
     * 获取当前页数据
     */
    getCurrentPageData() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const result = this.filteredData.slice(startIndex, endIndex);
        console.log(`[${this.containerId}] 获取当前页数据:`, {
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            totalItems: this.totalItems,
            startIndex,
            endIndex,
            resultLength: result.length,
            filteredDataLength: this.filteredData.length
        });
        return result;
    }

    /**
     * 获取总页数
     */
    getTotalPages() {
        return Math.ceil(this.totalItems / this.pageSize);
    }

    /**
     * 设置页面大小
     */
    setPageSize(size) {
        this.pageSize = size;
        this.currentPage = 1;
        this.savePageSize();
        return this;
    }

    /**
     * 跳转到指定页
     */
    goToPage(page) {
        const totalPages = this.getTotalPages();
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
        }
        return this;
    }

    /**
     * 上一页
     */
    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
        }
        return this;
    }

    /**
     * 下一页
     */
    nextPage() {
        if (this.currentPage < this.getTotalPages()) {
            this.currentPage++;
        }
        return this;
    }

    /**
     * 生成分页控制HTML
     */
    renderPaginationControls() {
        const totalPages = this.getTotalPages();
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalItems);

        return `
            <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <!-- 左侧：每页显示数量选择 -->
                <div class="pagination-size-selector">
                    <label style="margin-right: 10px; font-weight: 500;">每页显示：</label>
                    <select class="form-select form-select-sm" style="width: auto; display: inline-block;"
                            onchange="changePaginationSize('${this.containerId}', this.value)">
                        <option value="5" ${this.pageSize === 5 ? 'selected' : ''}>5条</option>
                        <option value="10" ${this.pageSize === 10 ? 'selected' : ''}>10条</option>
                        <option value="20" ${this.pageSize === 20 ? 'selected' : ''}>20条</option>
                        <option value="50" ${this.pageSize === 50 ? 'selected' : ''}>50条</option>
                        <option value="100" ${this.pageSize === 100 ? 'selected' : ''}>100条</option>
                    </select>
                </div>

                <!-- 中间：页面信息 -->
                <div class="pagination-info">
                    <span style="color: #666; font-size: 14px;">
                        显示第 ${startItem}-${endItem} 条，共 ${this.totalItems} 条记录
                    </span>
                </div>

                <!-- 右侧：页码导航 -->
                <div class="pagination-nav">
                    <nav aria-label="分页导航">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                                <button class="page-link" onclick="changePaginationPage('${this.containerId}', ${this.currentPage - 1})"
                                        ${this.currentPage === 1 ? 'disabled' : ''}>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                            </li>
                            ${this.renderPageNumbers()}
                            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                                <button class="page-link" onclick="changePaginationPage('${this.containerId}', ${this.currentPage + 1})"
                                        ${this.currentPage === totalPages ? 'disabled' : ''}>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        `;
    }

    /**
     * 生成页码数字
     */
    renderPageNumbers() {
        const totalPages = this.getTotalPages();
        const currentPage = this.currentPage;
        let pages = [];

        // 如果总页数小于等于7，显示所有页码
        if (totalPages <= 7) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // 复杂的页码显示逻辑
            if (currentPage <= 4) {
                pages = [1, 2, 3, 4, 5, '...', totalPages];
            } else if (currentPage >= totalPages - 3) {
                pages = [1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
            } else {
                pages = [1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages];
            }
        }

        return pages.map(page => {
            if (page === '...') {
                return `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            } else {
                return `
                    <li class="page-item ${page === currentPage ? 'active' : ''}">
                        <button class="page-link" onclick="changePaginationPage('${this.containerId}', ${page})">
                            ${page}
                        </button>
                    </li>
                `;
            }
        }).join('');
    }
}

// 分页管理器实例
let schoolsPagination = new PaginationManager('schools', 10);
let gradesPagination = new PaginationManager('grades', 10);
let studentsPagination = new PaginationManager('students', 10);

// ==================== 分页控制函数 ====================

/**
 * 改变分页大小
 */
function changePaginationSize(containerId, size) {
    console.log('分页大小改变函数被调用:', containerId, size); // 调试日志

    const pageSize = parseInt(size);
    let pagination;

    switch(containerId) {
        case 'schools':
            pagination = schoolsPagination;
            break;
        case 'grades':
            pagination = gradesPagination;
            break;
        case 'students':
            pagination = studentsPagination;
            break;
        default:
            console.error('未知的容器ID:', containerId);
            return;
    }

    console.log('设置页面大小:', pageSize);
    pagination.setPageSize(pageSize);

    // 重新渲染对应的表格
    switch(containerId) {
        case 'schools':
            showSchoolManagement();
            break;
        case 'grades':
            showGradeManagement();
            break;
        case 'students':
            showStudentManagement();
            break;
    }
}

/**
 * 改变当前页
 */
function changePaginationPage(containerId, page) {
    console.log('翻页函数被调用:', containerId, page); // 调试日志

    let pagination;

    switch(containerId) {
        case 'schools':
            pagination = schoolsPagination;
            break;
        case 'grades':
            pagination = gradesPagination;
            break;
        case 'students':
            pagination = studentsPagination;
            break;
        default:
            console.error('未知的容器ID:', containerId);
            return;
    }

    console.log('当前页面:', pagination.currentPage, '目标页面:', page);
    pagination.goToPage(page);
    console.log('翻页后页面:', pagination.currentPage);

    // 重新渲染对应的表格（只更新列表部分，不重新渲染整个页面）
    switch(containerId) {
        case 'schools':
            updateSchoolsList();
            break;
        case 'grades':
            updateGradesList();
            break;
        case 'students':
            updateStudentsList();
            break;
    }
}

// ==================== 配置常量 ====================
// 使用全局 CONFIG 对象（在 config.js 中定义）
const TEACHER_CONFIG = {
    ITEMS_PER_PAGE: 20,
    STORAGE_KEY: 'teacher_data'
};

// ==================== 列表更新函数 ====================

/**
 * 只更新学校列表（不重新渲染整个页面）
 */
function updateSchoolsList() {
    console.log('updateSchoolsList 被调用');
    const listContainer = document.getElementById('schools-list');
    if (listContainer) {
        listContainer.innerHTML = renderSchoolsList();
    }
}

/**
 * 只更新年级列表（不重新渲染整个页面）
 */
function updateGradesList() {
    console.log('updateGradesList 被调用');
    const listContainer = document.getElementById('grades-list');
    if (listContainer) {
        listContainer.innerHTML = renderGradesList();
    }
}

/**
 * 只更新学生列表（不重新渲染整个页面）
 */
function updateStudentsList() {
    console.log('updateStudentsList 被调用');
    const listContainer = document.getElementById('students-list');
    if (listContainer) {
        listContainer.innerHTML = renderStudentsList();
    }
}

// ==================== 工具函数 ====================

/**
 * 构建完整的API URL
 */
function buildApiUrl(path) {
    // 如果路径已经是完整URL，直接返回
    if (path.startsWith('http')) {
        return path;
    }

    // 确保路径以 / 开头
    if (!path.startsWith('/')) {
        path = '/' + path;
    }

    // 使用配置的基础URL
    return CONFIG.API.BASE_URL + path;
}

/**
 * API请求封装
 */
async function apiRequest(url, options = {}) {
    const token = localStorage.getItem('token');

    // 调试：检查 token 是否存在
    if (!token) {
        console.error('认证令牌不存在，请重新登录');
        showMessage('认证令牌不存在，请重新登录', 'error');
        // 重定向到登录页面
        window.location.href = '/index.html';
        return;
    }

    // 正确合并 headers，确保 Authorization 不被覆盖
    const defaultHeaders = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    };

    const mergedOptions = {
        ...options,
        headers: {
            ...defaultHeaders,
            ...(options.headers || {})
        },
        timeout: CONFIG.API.TIMEOUT
    };

    // 调试：打印请求信息
    console.log('API请求:', {
        url,
        method: mergedOptions.method || 'GET',
        headers: mergedOptions.headers,
        hasToken: !!token,
        tokenPrefix: token ? token.substring(0, 20) + '...' : 'null'
    });

    try {
        const fullUrl = buildApiUrl(url);
        const response = await fetch(fullUrl, mergedOptions);
        if (!response.ok) {
            if (response.status === 401) {
                console.error('认证失败，令牌可能已过期');
                showMessage('认证失败，请重新登录', 'error');
                // 清除过期的 token
                localStorage.removeItem('token');
                // 重定向到登录页面
                window.location.href = '/index.html';
                return;
            }
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    } catch (error) {
        console.error('API请求失败:', error);
        showMessage(`请求失败: ${error.message}`, 'error');
        throw error;
    }
}

/**
 * 显示消息提示
 */
function showMessage(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.getElementById('message-container') || document.body;
    const alertElement = document.createElement('div');
    alertElement.innerHTML = alertHtml;
    container.appendChild(alertElement.firstElementChild);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}



/**
 * 显示模态框
 */
function showModalSafely(modalId) {
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        return modal;
    }
    return null;
}

/**
 * 隐藏模态框
 */
function hideModalSafely(modalId) {
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        }
    }
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour12: false });
}

// ==================== 初始化函数 ====================

/**
 * 初始化教师管理系统
 */
async function initTeacherManagement() {
    console.log('🚀 初始化教师管理系统 2.0');

    try {
        // 验证用户身份
        await validateUser();

        // 初始化界面
        initUI();

        // 加载基础数据
        await loadBaseData();

        // 显示默认页面
        showSection('dashboard');

        console.log('✅ 教师管理系统初始化完成');
    } catch (error) {
        console.error('❌ 初始化失败:', error);
        showMessage('系统初始化失败，请刷新页面重试', 'error');
    }
}

/**
 * 验证用户身份
 */
async function validateUser() {
    const token = localStorage.getItem('token');
    if (!token) {
        // 没有token，跳转到主页而不是登录页
        window.location.href = '/';
        return;
    }

    try {
        const userData = await apiRequest('/api/auth/validate');
        currentUser = userData.user;

        if (currentUser.role !== 'teacher' && currentUser.role !== 'admin') {
            throw new Error('权限不足');
        }

        console.log('用户验证成功:', currentUser);
    } catch (error) {
        console.error('用户验证失败:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // 验证失败，跳转到主页
        window.location.href = '/';
    }
}

/**
 * 初始化界面
 */
function initUI() {
    // 设置用户信息
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = currentUser.display_name || currentUser.username;
    }

    // 绑定导航事件
    bindNavigationEvents();
    
    // 绑定全局事件
    bindGlobalEvents();
}

/**
 * 绑定导航事件
 */
function bindNavigationEvents() {
    const navLinks = document.querySelectorAll('[data-section]');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = e.target.closest('[data-section]').dataset.section;
            showSection(section);
        });
    });
}

/**
 * 绑定全局事件
 */
function bindGlobalEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }
}

/**
 * 加载基础数据
 */
async function loadBaseData() {
    console.log('📊 加载基础数据...');
    
    try {
        // 并行加载数据
        const [schoolsData, gradesData, studentsData] = await Promise.allSettled([
            loadSchools(),
            loadGrades(),
            loadStudents()
        ]);

        // 处理结果
        schools = schoolsData.status === 'fulfilled' ? schoolsData.value : [];
        grades = gradesData.status === 'fulfilled' ? gradesData.value : [];
        students = studentsData.status === 'fulfilled' ? studentsData.value : [];

        console.log('基础数据加载完成:', {
            schools: schools.length,
            grades: grades.length,
            students: students.length
        });
    } catch (error) {
        console.error('基础数据加载失败:', error);
    }
}

// ==================== 数据加载函数 ====================

/**
 * 加载学校数据
 */
async function loadSchools() {
    try {
        const data = await apiRequest('/api/teacher/schools');
        return Array.isArray(data) ? data : (data.data || []);
    } catch (error) {
        console.error('加载学校数据失败:', error);
        return [];
    }
}

/**
 * 加载年级数据
 */
async function loadGrades() {
    try {
        const data = await apiRequest('/api/teacher/classes');

        let grades = [];
        if (Array.isArray(data)) {
            grades = data;
        } else if (data && Array.isArray(data.data)) {
            grades = data.data;
        } else if (data && data.success && Array.isArray(data.data)) {
            grades = data.data;
        } else {
            console.warn('年级数据格式不正确:', data);
            grades = [];
        }

        return grades;
    } catch (error) {
        console.error('加载年级数据失败:', error);
        return [];
    }
}

/**
 * 加载学生数据
 */
async function loadStudents() {
    try {
        const data = await apiRequest('/api/teacher/students');
        return Array.isArray(data) ? data : (data.data || []);
    } catch (error) {
        console.error('加载学生数据失败:', error);
        return [];
    }
}

// ==================== 页面显示函数 ====================

/**
 * 显示指定页面
 */
function showSection(section) {
    console.log('显示页面:', section);
    
    currentSection = section;
    
    // 更新导航状态
    updateNavigation(section);
    
    // 显示对应内容
    const contentArea = document.getElementById('content-area');
    if (!contentArea) return;

    switch (section) {
        case 'dashboard':
            showDashboard();
            break;
        case 'schools':
            showSchoolManagement();
            break;
        case 'grades':
            showGradeManagement();
            break;
        case 'students':
            showStudentManagement();
            break;
        default:
            showDashboard();
    }
}

/**
 * 更新导航状态
 */
function updateNavigation(activeSection) {
    const navLinks = document.querySelectorAll('[data-section]');
    navLinks.forEach(link => {
        const section = link.dataset.section;
        if (section === activeSection) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// ==================== 工具函数 ====================

/**
 * 退出登录
 */
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // 跳转到主页
        window.location.href = '/';
    }
}

// ==================== 控制台预览 ====================

/**
 * 显示控制台预览
 */
function showDashboard() {
    const contentArea = document.getElementById('content-area');

    const dashboardHtml = `
        <div class="dashboard-container">
            <div class="page-header">
                <h2><i class="fas fa-tachometer-alt"></i> 控制台预览</h2>
                <p class="text-muted">欢迎使用教师管理系统</p>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stat-icon">
                            <i class="fas fa-school"></i>
                        </div>
                        <div class="stat-content">
                            <h3>${schools.length}</h3>
                            <p>管理学校</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card bg-success">
                        <div class="stat-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="stat-content">
                            <h3>${getUniqueGradeCount()}</h3>
                            <p>年级班级</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card bg-info">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>${students.length}</h3>
                            <p>学生总数</p>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 快速操作 -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-bolt"></i> 快速操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="quick-actions">
                                <button class="btn btn-primary" onclick="showSection('schools')">
                                    <i class="fas fa-school"></i> 管理学校
                                </button>
                                <button class="btn btn-success" onclick="showSection('grades')">
                                    <i class="fas fa-layer-group"></i> 管理年级
                                </button>
                                <button class="btn btn-info" onclick="showSection('students')">
                                    <i class="fas fa-users"></i> 管理学生
                                </button>
                                <button class="btn btn-secondary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line"></i> 学校分布</h5>
                        </div>
                        <div class="card-body">
                            ${renderSchoolDistribution()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-users"></i> 学生分布</h5>
                        </div>
                        <div class="card-body">
                            ${renderStudentDistribution()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    contentArea.innerHTML = dashboardHtml;
}

/**
 * 获取唯一年级数量
 */
function getUniqueGradeCount() {
    const uniqueGrades = new Set();
    grades.forEach(grade => {
        uniqueGrades.add(`${grade.school_id}-${grade.grade}-${grade.class}`);
    });
    return uniqueGrades.size;
}

/**
 * 渲染学校分布
 */
function renderSchoolDistribution() {
    if (schools.length === 0) {
        return '<p class="text-muted">暂无学校数据</p>';
    }

    return schools.map(school => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>${school.name}</span>
            <span class="badge bg-primary">${getStudentCountBySchool(school.id)} 人</span>
        </div>
    `).join('');
}

/**
 * 渲染学生分布
 */
function renderStudentDistribution() {
    if (students.length === 0) {
        return '<p class="text-muted">暂无学生数据</p>';
    }

    const gradeDistribution = {};
    students.forEach(student => {
        const key = `${student.grade}年级`;
        gradeDistribution[key] = (gradeDistribution[key] || 0) + 1;
    });

    return Object.entries(gradeDistribution).map(([grade, count]) => `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <span>${grade}</span>
            <span class="badge bg-info">${count} 人</span>
        </div>
    `).join('');
}

/**
 * 获取指定学校的学生数量
 */
function getStudentCountBySchool(schoolId) {
    return students.filter(student => student.school_id === schoolId).length;
}

/**
 * 刷新数据
 */
async function refreshData() {
    showMessage('正在刷新数据...', 'info');
    try {
        await loadBaseData();
        showSection(currentSection); // 重新渲染当前页面
        showMessage('数据刷新成功', 'success');
    } catch (error) {
        showMessage('数据刷新失败', 'error');
    }
}

// ==================== 页面加载完成后初始化 ====================
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 页面加载完成，开始初始化...');
    initTeacherManagement();
});

// ==================== 学校管理 ====================

/**
 * 显示学校管理页面
 */
function showSchoolManagement() {
    const contentArea = document.getElementById('content-area');

    const schoolHtml = `
        <div class="school-management">
            <div class="page-header">
                <h2><i class="fas fa-school"></i> 学校管理</h2>
                <button class="btn btn-primary" onclick="showAddSchoolModal()">
                    <i class="fas fa-plus"></i> 添加学校
                </button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="school-search"
                                   placeholder="搜索学校名称..." onkeyup="filterSchools()">
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-secondary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学校列表 -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 学校列表 (${schools.length})</h5>
                </div>
                <div class="card-body">
                    <div id="schools-list">
                        ${renderSchoolsList(true)}
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加学校模态框 -->
        <div class="modal fade" id="addSchoolModal" tabindex="-1" aria-labelledby="addSchoolModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addSchoolModalLabel">添加学校</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addSchoolForm">
                            <div class="mb-3">
                                <label for="schoolName" class="form-label">学校名称 *</label>
                                <input type="text" class="form-control" id="schoolName" required>
                            </div>
                            <div class="mb-3">
                                <label for="schoolAddress" class="form-label">学校地址</label>
                                <textarea class="form-control" id="schoolAddress" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="schoolPhone" class="form-label">联系电话</label>
                                <input type="tel" class="form-control" id="schoolPhone">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitAddSchool()">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑学校模态框 -->
        <div class="modal fade" id="editSchoolModal" tabindex="-1" aria-labelledby="editSchoolModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editSchoolModalLabel">编辑学校信息</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editSchoolForm">
                            <input type="hidden" id="editSchoolId">
                            <div class="mb-3">
                                <label for="editSchoolName" class="form-label">学校名称 *</label>
                                <input type="text" class="form-control" id="editSchoolName" required>
                            </div>
                            <div class="mb-3">
                                <label for="editSchoolAddress" class="form-label">学校地址</label>
                                <textarea class="form-control" id="editSchoolAddress" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="editSchoolPhone" class="form-label">联系电话</label>
                                <input type="tel" class="form-control" id="editSchoolPhone">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitEditSchool()">保存修改</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    contentArea.innerHTML = schoolHtml;
}

/**
 * 渲染学校列表（支持分页）
 */
function renderSchoolsList(resetPagination = false) {
    // 只在需要时重新设置分页数据
    if (resetPagination || schoolsPagination.data.length !== schools.length) {
        console.log('重置学校分页数据');
        schoolsPagination.setData(schools);
    }

    if (schools.length === 0) {
        return `
            <div class="text-center py-4">
                <i class="fas fa-school fa-3x text-muted mb-3"></i>
                <h5>暂无学校数据</h5>
                <p class="text-muted">点击上方"添加学校"按钮开始添加</p>
            </div>
        `;
    }

    // 获取当前页数据
    const currentPageSchools = schoolsPagination.getCurrentPageData();

    return `
        <div class="table-responsive">
            <table class="table" style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <thead style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <tr>
                        <th style="padding: 15px; font-weight: 600;">学校名称</th>
                        <th style="padding: 15px; font-weight: 600;">地址</th>
                        <th style="padding: 15px; font-weight: 600;">联系电话</th>
                        <th style="padding: 15px; font-weight: 600;">学生数量</th>
                        <th style="padding: 15px; font-weight: 600; text-align: center;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${currentPageSchools.map(school => `
                        <tr style="border-bottom: 1px solid #dee2e6;">
                            <td style="padding: 15px;">
                                <strong>${school.name}</strong>
                            </td>
                            <td style="padding: 15px;">${school.address || '-'}</td>
                            <td style="padding: 15px;">${school.contact_phone || '-'}</td>
                            <td style="padding: 15px;">
                                <span class="badge bg-primary">${getStudentCountBySchool(school.id)}</span>
                            </td>
                            <td style="padding: 15px; text-align: center;">
                                <button onclick="editSchool(${school.id})" class="btn btn-sm btn-success me-1">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button onclick="deleteSchool(${school.id})" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        ${schoolsPagination.renderPaginationControls()}
    `;
}

/**
 * 显示添加学校模态框
 */
function showAddSchoolModal() {
    showModalSafely('addSchoolModal');
}

/**
 * 提交添加学校
 */
async function submitAddSchool() {
    const form = document.getElementById('addSchoolForm');
    const formData = new FormData(form);

    const schoolData = {
        name: document.getElementById('schoolName').value.trim(),
        address: document.getElementById('schoolAddress').value.trim(),
        contact_phone: document.getElementById('schoolPhone').value.trim()
    };

    if (!schoolData.name) {
        showMessage('请输入学校名称', 'error');
        return;
    }

    try {
        console.log('发送学校数据:', schoolData);

        const response = await apiRequest('/api/teacher/schools', {
            method: 'POST',
            body: JSON.stringify(schoolData)
        });

        console.log('学校添加响应:', response);
        showMessage('学校添加成功', 'success');

        // 关闭模态框
        hideModalSafely('addSchoolModal');

        // 重新加载数据
        schools = await loadSchools();
        showSchoolManagement();

    } catch (error) {
        console.error('添加学校失败:', error);
        showMessage(`添加学校失败: ${error.message}`, 'error');
    }
}

/**
 * 筛选学校（支持分页）
 */
function filterSchools() {
    const searchTerm = document.getElementById('school-search').value.toLowerCase();
    const filteredSchools = schools.filter(school =>
        school.name.toLowerCase().includes(searchTerm)
    );

    // 设置筛选后的数据到分页管理器
    schoolsPagination.setFilteredData(filteredSchools);

    const listContainer = document.getElementById('schools-list');
    if (listContainer) {
        // 获取当前页数据并渲染
        const currentPageSchools = schoolsPagination.getCurrentPageData();

        listContainer.innerHTML = `
            <div class="table-responsive">
                <table class="table" style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <thead style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <tr>
                            <th style="padding: 15px; font-weight: 600;">学校名称</th>
                            <th style="padding: 15px; font-weight: 600;">地址</th>
                            <th style="padding: 15px; font-weight: 600;">联系电话</th>
                            <th style="padding: 15px; font-weight: 600;">学生数量</th>
                            <th style="padding: 15px; font-weight: 600; text-align: center;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${currentPageSchools.length === 0 ? `
                            <tr>
                                <td colspan="5" style="padding: 30px; text-align: center; color: #666;">
                                    <i class="fas fa-search fa-2x mb-3" style="opacity: 0.5;"></i>
                                    <p>没有找到匹配的学校</p>
                                </td>
                            </tr>
                        ` : currentPageSchools.map(school => `
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td style="padding: 15px;">
                                    <strong>${school.name}</strong>
                                </td>
                                <td style="padding: 15px;">${school.address || '-'}</td>
                                <td style="padding: 15px;">${school.contact_phone || '-'}</td>
                                <td style="padding: 15px;">
                                    <span class="badge bg-primary">${getStudentCountBySchool(school.id)}</span>
                                </td>
                                <td style="padding: 15px; text-align: center;">
                                    <button onclick="editSchool(${school.id})" class="btn btn-sm btn-success me-1">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button onclick="deleteSchool(${school.id})" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            ${schoolsPagination.renderPaginationControls()}
        `;
    }
}

/**
 * 编辑学校
 */
function editSchool(schoolId) {
    const school = schools.find(s => s.id === schoolId);
    if (!school) {
        showMessage('学校信息不存在', 'error');
        return;
    }

    // 填充编辑表单
    document.getElementById('editSchoolId').value = school.id;
    document.getElementById('editSchoolName').value = school.name || '';
    document.getElementById('editSchoolAddress').value = school.address || '';
    document.getElementById('editSchoolPhone').value = school.contact_phone || '';

    // 显示编辑模态框
    showModalSafely('editSchoolModal');
}

/**
 * 提交编辑学校
 */
async function submitEditSchool() {
    const schoolId = document.getElementById('editSchoolId').value;
    const schoolData = {
        name: document.getElementById('editSchoolName').value.trim(),
        address: document.getElementById('editSchoolAddress').value.trim(),
        phone: document.getElementById('editSchoolPhone').value.trim()
    };

    if (!schoolData.name) {
        showMessage('请输入学校名称', 'error');
        return;
    }

    try {
        const response = await apiRequest(`/api/teacher/schools/${schoolId}`, {
            method: 'PUT',
            body: JSON.stringify(schoolData)
        });

        console.log('学校编辑响应:', response);
        showMessage('学校信息更新成功', 'success');

        // 关闭模态框
        hideModalSafely('editSchoolModal');

        // 重新加载数据
        schools = await loadSchools();
        showSchoolManagement();

    } catch (error) {
        console.error('编辑学校失败:', error);
        showMessage(`编辑学校失败: ${error.message}`, 'error');
    }
}

/**
 * 删除学校
 */
async function deleteSchool(schoolId) {
    const school = schools.find(s => s.id === schoolId);
    if (!school) {
        showMessage('学校信息不存在', 'error');
        return;
    }

    // 获取学校关联的学生数量
    const studentCount = getStudentCountBySchool(schoolId);
    let confirmMessage = `确定要删除学校"${school.name}"吗？`;

    if (studentCount > 0) {
        confirmMessage += `\n\n注意：该学校还有 ${studentCount} 名学生，删除学校可能会影响学生数据。`;
    }

    if (!confirm(confirmMessage)) return;

    try {
        const response = await apiRequest(`/api/teacher/schools/${schoolId}`, {
            method: 'DELETE'
        });

        console.log('学校删除响应:', response);
        showMessage('学校删除成功', 'success');

        // 重新加载数据
        schools = await loadSchools();
        showSchoolManagement();

    } catch (error) {
        console.error('删除学校失败:', error);
        showMessage(`删除学校失败: ${error.message}`, 'error');
    }
}

// ==================== 年级管理 ====================

/**
 * 显示年级管理页面
 */
function showGradeManagement() {
    const contentArea = document.getElementById('content-area');

    const gradeHtml = `
        <div class="grade-management">
            <div class="page-header">
                <h2><i class="fas fa-layer-group"></i> 年级管理</h2>
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="showAddGradeModal()">
                        <i class="fas fa-plus"></i> 添加年级班级
                    </button>
                    <button class="btn btn-warning" onclick="batchUpgradeGrades()">
                        <i class="fas fa-arrow-up"></i> 一键升年级
                    </button>
                    <button class="btn btn-danger" onclick="batchDeleteGrades()">
                        <i class="fas fa-trash-alt"></i> 批量删除
                    </button>
                </div>
            </div>

            <!-- 筛选器 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <select class="form-select" id="school-filter" onchange="filterGrades()">
                                <option value="">全部学校</option>
                                ${schools.map(school => `
                                    <option value="${school.id}">${school.name}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="grade-filter" onchange="filterGrades()">
                                <option value="">全部年级</option>
                                ${[1,2,3,4,5,6,7,8,9,10,11,12].map(grade => `
                                    <option value="${grade}">${grade}年级</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-secondary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 年级列表 -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 年级班级列表 (${grades.length})</h5>
                </div>
                <div class="card-body">
                    <div id="grades-list">
                        ${renderGradesList(true)}
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加年级班级模态框 -->
        <div class="modal fade" id="addGradeModal" tabindex="-1" aria-labelledby="addGradeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addGradeModalLabel">添加年级班级配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addGradeForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="gradeSchool" class="form-label">选择学校 *</label>
                                    <select class="form-select" id="gradeSchool" required>
                                        <option value="">请选择学校</option>
                                        ${schools.map(school => `
                                            <option value="${school.id}">${school.name}</option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="gradeLevel" class="form-label">年级 *</label>
                                    <select class="form-select" id="gradeLevel" required onchange="updateClassCountOptions()">
                                        <option value="">请选择年级</option>
                                        ${[1,2,3,4,5,6,7,8,9,10,11,12].map(grade => `
                                            <option value="${grade}">${grade}年级</option>
                                        `).join('')}
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="classCount" class="form-label">该年级班级数量 *</label>
                                <input type="number" class="form-control" id="classCount" required
                                       min="1" max="50" placeholder="请输入班级数量"
                                       onchange="generateClassOptions()" oninput="generateClassOptions()">
                                <div class="form-text">设置该年级总共有多少个班级（1-50个班）</div>
                            </div>

                            <div class="mb-3" id="teachingClassesSection" style="display: none;">
                                <label class="form-label">选择您任教的班级</label>
                                <div class="mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllClasses()">全选</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="clearAllClasses()">清空</button>
                                </div>
                                <div id="teachingClassOptions" class="border rounded p-3" style="min-height: 80px;">
                                    <!-- 动态生成班级选项 -->
                                </div>
                                <div class="form-text">可以选择多个班级，也可以不选择（稍后再分配）</div>
                            </div>

                            <div class="mb-3">
                                <label for="gradeNotes" class="form-label">备注</label>
                                <textarea class="form-control" id="gradeNotes" rows="2" placeholder="可选：添加年级班级相关备注"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitAddGrade()">保存配置</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    contentArea.innerHTML = gradeHtml;
}

/**
 * 渲染年级列表（支持分页）
 */
function renderGradesList(resetPagination = false) {
    // 只在需要时重新设置分页数据
    if (resetPagination || gradesPagination.data.length !== grades.length) {
        console.log('重置年级分页数据');
        gradesPagination.setData(grades);
    }

    if (grades.length === 0) {
        return `
            <div class="text-center py-4">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h5>暂无年级班级数据</h5>
                <p class="text-muted">点击上方"添加年级班级"按钮开始添加</p>
            </div>
        `;
    }

    // 获取当前页数据
    const currentPageGrades = gradesPagination.getCurrentPageData();

    return `
        <div class="table-responsive">
            <table class="table" style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <thead style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <tr>
                        <th style="padding: 15px; font-weight: 600;">
                            <input type="checkbox" id="selectAllGrades" onchange="toggleAllGrades(this)">
                        </th>
                        <th style="padding: 15px; font-weight: 600;">学校</th>
                        <th style="padding: 15px; font-weight: 600;">年级</th>
                        <th style="padding: 15px; font-weight: 600;">班级</th>
                        <th style="padding: 15px; font-weight: 600;">学生数量</th>
                        <th style="padding: 15px; font-weight: 600; text-align: center;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${currentPageGrades.map((grade, index) => {
                        console.log(`渲染年级 ${index}:`, grade); // 调试信息

                        const school = schools.find(s => s.id === grade.school_id);
                        const studentCount = students.filter(s =>
                            s.school_id === grade.school_id &&
                            s.grade === grade.grade &&
                            s.class === grade.class
                        ).length;

                        // 确保ID存在且有效 - 优先使用数字ID
                        let gradeId = null;

                        // 使用数据库主键ID
                        gradeId = grade.id;
                        const isValidId = gradeId && (typeof gradeId === 'number' || !isNaN(parseInt(gradeId)));

                        return `
                            <tr data-grade-id="${gradeId}" style="border-bottom: 1px solid #dee2e6;">
                                <td style="padding: 15px;">
                                    <input type="checkbox" class="grade-checkbox" value="${gradeId}" ${!isValidId ? 'disabled' : ''}>
                                </td>
                                <td style="padding: 15px;">${school ? school.name : '未知学校'}</td>
                                <td style="padding: 15px;">${grade.grade}年级</td>
                                <td style="padding: 15px;">${grade.class}班</td>
                                <td style="padding: 15px;">
                                    <span class="badge bg-info">${studentCount}</span>
                                </td>
                                <td style="padding: 15px; text-align: center;">
                                    ${isValidId ? `
                                        <button onclick="deleteGrade('${gradeId}')" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    ` : `
                                        <span class="text-muted">无效ID</span>
                                    `}
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
        ${gradesPagination.renderPaginationControls()}
    `;
}

/**
 * 显示添加年级模态框
 */
function showAddGradeModal() {
    // 重置表单
    document.getElementById('addGradeForm').reset();
    document.getElementById('teachingClassesSection').style.display = 'none';
    document.getElementById('classCount').innerHTML = '<option value="">请先选择年级</option>';

    showModalSafely('addGradeModal');
}

/**
 * 更新班级数量选项
 */
function updateClassCountOptions() {
    const gradeLevel = document.getElementById('gradeLevel').value;
    const classCountSelect = document.getElementById('classCount');

    if (!gradeLevel) {
        classCountSelect.innerHTML = '<option value="">请先选择年级</option>';
        document.getElementById('teachingClassesSection').style.display = 'none';
        return;
    }

    // 启用班级数量选择
    classCountSelect.innerHTML = `
        <option value="">请选择班级数量</option>
        ${[1,2,3,4,5,6,7,8,9,10,12,15,20].map(count => `
            <option value="${count}">${count}个班</option>
        `).join('')}
    `;
}

/**
 * 生成班级选项
 */
function generateClassOptions() {
    const classCount = parseInt(document.getElementById('classCount').value);
    const teachingSection = document.getElementById('teachingClassesSection');
    const optionsDiv = document.getElementById('teachingClassOptions');

    if (!classCount || classCount < 1 || classCount > 50) {
        teachingSection.style.display = 'none';
        return;
    }

    // 生成班级复选框
    const classOptions = [];
    for (let i = 1; i <= classCount; i++) {
        classOptions.push(`
            <div class="col-md-3 mb-2">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="teachingClasses" value="${i}" id="teaching_class_${i}">
                    <label class="form-check-label" for="teaching_class_${i}">
                        ${i}班
                    </label>
                </div>
            </div>
        `);
    }

    optionsDiv.innerHTML = `
        <div class="row">
            ${classOptions.join('')}
        </div>
        <div class="mt-2">
            <small class="text-muted">
                将创建 ${classCount} 个班级：${Array.from({length: classCount}, (_, i) => `${i+1}班`).join('、')}
            </small>
        </div>
    `;

    teachingSection.style.display = 'block';
}

/**
 * 全选班级
 */
function selectAllClasses() {
    const checkboxes = document.querySelectorAll('input[name="teachingClasses"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

/**
 * 清空班级选择
 */
function clearAllClasses() {
    const checkboxes = document.querySelectorAll('input[name="teachingClasses"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

/**
 * 提交添加年级
 */
async function submitAddGrade() {
    const schoolId = document.getElementById('gradeSchool').value;
    const grade = document.getElementById('gradeLevel').value;
    const classCount = document.getElementById('classCount').value;
    const selectedClasses = Array.from(document.querySelectorAll('input[name="teachingClasses"]:checked')).map(cb => parseInt(cb.value));
    const notes = document.getElementById('gradeNotes').value.trim();

    if (!schoolId || !grade || !classCount) {
        showMessage('请选择学校、年级和班级数量', 'error');
        return;
    }

    try {
        console.log('添加年级配置:', {
            schoolId: parseInt(schoolId),
            grade: parseInt(grade),
            classCount: parseInt(classCount),
            selectedClasses,
            notes
        });

        // 1. 记录年级配置信息（暂时跳过grade-configs API）
        console.log('创建年级配置:', {
            school_id: parseInt(schoolId),
            grade: parseInt(grade),
            class_count: parseInt(classCount),
            notes: notes || null
        });

        // 2. 为每个班级创建班级记录（直接创建班级权限）
        for (let classNum = 1; classNum <= parseInt(classCount); classNum++) {
            try {
                await apiRequest('/api/teacher/class-permissions', {
                    method: 'POST',
                    body: JSON.stringify({
                        school_id: parseInt(schoolId),
                        grade: parseInt(grade),
                        class: classNum
                    })
                });
            } catch (classError) {
                console.warn(`班级 ${classNum} 可能已存在:`, classError);
            }
        }

        // 3. 统计成功创建的班级数量
        let successCount = 0;
        let teachingCount = 0;

        // 检查哪些班级创建成功
        for (let classNum = 1; classNum <= parseInt(classCount); classNum++) {
            successCount++;
            if (selectedClasses.includes(classNum)) {
                teachingCount++;
            }
        }

        if (teachingCount > 0) {
            showMessage(`年级配置创建成功！共创建 ${successCount} 个班级，您已获得 ${teachingCount} 个班级的任教权限`, 'success');
        } else {
            showMessage(`年级配置创建成功！共创建 ${successCount} 个班级`, 'success');
        }

        // 关闭模态框
        hideModalSafely('addGradeModal');

        // 重新加载数据
        grades = await loadGrades();
        showGradeManagement();

    } catch (error) {
        console.error('添加年级配置失败:', error);
        showMessage(`添加年级配置失败: ${error.message}`, 'error');
    }
}

/**
 * 筛选年级
 */
function filterGrades() {
    const schoolFilter = document.getElementById('school-filter').value;
    const gradeFilter = document.getElementById('grade-filter').value;

    let filteredGrades = grades;

    if (schoolFilter) {
        filteredGrades = filteredGrades.filter(grade => grade.school_id == schoolFilter);
    }

    if (gradeFilter) {
        filteredGrades = filteredGrades.filter(grade => grade.grade == gradeFilter);
    }

    const listContainer = document.getElementById('grades-list');
    if (listContainer) {
        // 临时替换grades数组进行渲染
        const originalGrades = grades;
        grades = filteredGrades;
        listContainer.innerHTML = renderGradesList();
        grades = originalGrades;
    }
}

/**
 * 删除年级
 */
async function deleteGrade(gradeId) {
    // 优化：直接从本地数组查找，避免不必要的API调用
    const grade = grades.find(g => g.id == gradeId);

    if (!grade) {
        showMessage('该记录已不存在，请刷新页面', 'warning');
        // 只有在找不到记录时才重新加载数据
        grades = await loadGrades();
        showGradeManagement();
        return;
    }

    const school = schools.find(s => s.id === grade.school_id);
    const gradeName = `${school ? school.name : '未知学校'} ${grade.grade}年级${grade.class}班`;

    // 检查是否有学生在该班级
    const studentsInClass = students.filter(s =>
        s.school_id === grade.school_id &&
        s.grade == grade.grade &&
        s.class == grade.class
    );

    let confirmMessage = `确定要删除"${gradeName}"吗？\n\n注意：这将删除您对该班级的任教权限。`;

    if (studentsInClass.length > 0) {
        confirmMessage += `\n\n该班级还有 ${studentsInClass.length} 名学生，删除后这些学生将无法被管理。`;
    }

    if (!confirm(confirmMessage)) return;

    try {
        const response = await apiRequest(`/api/teacher/class-permissions/${gradeId}`, {
            method: 'DELETE'
        });

        console.log('删除年级班级响应:', response);

        // 显示删除结果，包括级联删除的学生数量
        let successMessage = response.message || '年级班级删除成功';
        showMessage(successMessage, 'success');

        // 优化：直接从本地数组移除，避免重新查询
        grades = grades.filter(g => g.id != gradeId);

        // 如果删除了学生，也需要更新本地学生数组
        if (response.deletedStudents > 0) {
            const deletedGrade = grade;
            students = students.filter(s =>
                !(s.school_id === deletedGrade.school_id &&
                  s.grade == deletedGrade.grade &&
                  s.class == deletedGrade.class)
            );
        }

        showGradeManagement();

    } catch (error) {
        console.error('删除年级班级失败:', error);
        showMessage(`删除年级班级失败: ${error.message}`, 'error');
    }
}

/**
 * 切换所有年级的选择状态
 */
function toggleAllGrades(checkbox) {
    const gradeCheckboxes = document.querySelectorAll('.grade-checkbox:not([disabled])');
    gradeCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
}

/**
 * 批量删除年级
 */
async function batchDeleteGrades() {
    const selectedCheckboxes = document.querySelectorAll('.grade-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        showMessage('请先选择要删除的年级班级', 'warning');
        return;
    }

    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    const selectedGrades = grades.filter(g => selectedIds.includes(String(g.id)));

    // 统计将要删除的学生数量
    let totalStudents = 0;
    const gradeNames = selectedGrades.map(g => {
        const school = schools.find(s => s.id === g.school_id);
        const studentsInClass = students.filter(s =>
            s.school_id === g.school_id &&
            s.grade == g.grade &&
            s.class == g.class
        ).length;
        totalStudents += studentsInClass;
        return `${school ? school.name : '未知学校'} ${g.grade}年级${g.class}班 (${studentsInClass}名学生)`;
    }).join('\n');

    let confirmMessage = `确定要删除以下 ${selectedIds.length} 个年级班级吗？\n\n${gradeNames}\n\n注意：这将删除您对这些班级的任教权限。`;

    if (totalStudents > 0) {
        confirmMessage += `\n\n⚠️ 警告：将同时删除 ${totalStudents} 名学生的数据！`;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    // 显示进度提示
    showMessage('正在批量删除，请稍候...', 'info');

    let successCount = 0;
    let failCount = 0;
    let totalDeletedStudents = 0;

    // 优化：使用并行删除提高速度
    const deletePromises = selectedIds.map(async (gradeId) => {
        try {
            const response = await apiRequest(`/api/teacher/class-permissions/${gradeId}`, {
                method: 'DELETE'
            });
            return {
                success: true,
                gradeId,
                deletedStudents: response.deletedStudents || 0
            };
        } catch (error) {
            console.error(`删除年级 ${gradeId} 失败:`, error);
            return {
                success: false,
                gradeId,
                error: error.message
            };
        }
    });

    // 等待所有删除操作完成
    const results = await Promise.all(deletePromises);

    // 统计结果
    results.forEach(result => {
        if (result.success) {
            successCount++;
            totalDeletedStudents += result.deletedStudents;
        } else {
            failCount++;
        }
    });

    if (successCount > 0) {
        let message = `成功删除 ${successCount} 个年级班级`;
        if (totalDeletedStudents > 0) {
            message += `，同时删除了 ${totalDeletedStudents} 名学生数据`;
        }
        if (failCount > 0) {
            message += `，失败 ${failCount} 个`;
        }

        showMessage(message, failCount > 0 ? 'warning' : 'success');

        // 重新加载数据以确保一致性
        grades = await loadGrades();
        students = await loadStudents();
        showGradeManagement();
    } else {
        showMessage('批量删除失败', 'error');
    }
}

/**
 * 一键升年级
 */
async function batchUpgradeGrades() {
    if (grades.length === 0) {
        showMessage('没有年级数据可以升级', 'warning');
        return;
    }

    // 按学校分组统计年级
    const schoolGrades = {};
    grades.forEach(grade => {
        const schoolId = grade.school_id;
        if (!schoolGrades[schoolId]) {
            schoolGrades[schoolId] = new Set();
        }
        schoolGrades[schoolId].add(grade.grade);
    });

    // 生成升级预览
    let upgradePreview = '';
    Object.keys(schoolGrades).forEach(schoolId => {
        const school = schools.find(s => s.id == schoolId);
        const schoolName = school ? school.name : '未知学校';
        const gradeList = Array.from(schoolGrades[schoolId]).sort((a, b) => a - b);

        upgradePreview += `${schoolName}:\n`;
        gradeList.forEach(grade => {
            if (grade < 9) { // 最高到9年级
                upgradePreview += `  ${grade}年级 → ${grade + 1}年级\n`;
            } else {
                upgradePreview += `  ${grade}年级 → 毕业删除\n`;
            }
        });
        upgradePreview += '\n';
    });

    if (!confirm(`确定要执行一键升年级操作吗？\n\n升级预览：\n${upgradePreview}\n注意：\n- 9年级将被删除（毕业）\n- 此操作不可撤销`)) {
        return;
    }

    let upgradeCount = 0;
    let deleteCount = 0;
    let failCount = 0;

    for (const grade of grades) {
        try {
            if (grade.grade >= 9) {
                // 9年级毕业，删除记录
                await apiRequest(`/api/teacher/class-permissions/${grade.id}`, {
                    method: 'DELETE'
                });
                deleteCount++;
            } else {
                // 升级到下一年级
                await apiRequest(`/api/teacher/class-permissions/${grade.id}`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        grade: grade.grade + 1
                    })
                });
                upgradeCount++;
            }
        } catch (error) {
            console.error(`处理年级 ${grade.id} 失败:`, error);
            failCount++;
        }
    }

    showMessage(`升年级完成！升级 ${upgradeCount} 个班级，毕业删除 ${deleteCount} 个班级${failCount > 0 ? `，失败 ${failCount} 个` : ''}`,
               failCount > 0 ? 'warning' : 'success');

    // 重新加载数据
    grades = await loadGrades();
    showGradeManagement();
}

// ==================== 学生管理 ====================

/**
 * 显示学生管理页面
 */
function showStudentManagement() {
    const contentArea = document.getElementById('content-area');

    const studentHtml = `
        <div class="student-management">
            <div class="page-header">
                <h2><i class="fas fa-users"></i> 学生管理</h2>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="showBatchImportModal()">
                        <i class="fas fa-file-excel"></i> 批量导入
                    </button>
                    <button class="btn btn-primary" onclick="showAddStudentModal()">
                        <i class="fas fa-plus"></i> 添加学生
                    </button>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="student-search"
                                   placeholder="搜索学生姓名..." onkeyup="filterStudents()">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-school-filter" onchange="filterStudents()">
                                <option value="">全部学校</option>
                                ${schools.map(school => `
                                    <option value="${school.id}">${school.name}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="student-grade-filter" onchange="filterStudents()">
                                <option value="">全部年级</option>
                                ${[1,2,3,4,5,6,7,8,9,10,11,12].map(grade => `
                                    <option value="${grade}">${grade}年级</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-secondary" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学生列表 -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 学生列表 (${students.length})</h5>
                </div>
                <div class="card-body">
                    <div id="students-list">
                        ${renderStudentsList(true)}
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加学生模态框 -->
        <div class="modal fade" id="addStudentModal" tabindex="-1" aria-labelledby="addStudentModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addStudentModalLabel">添加学生</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addStudentForm">
                            <div class="mb-3">
                                <label for="studentName" class="form-label">学生姓名 *</label>
                                <input type="text" class="form-control" id="studentName" required>
                            </div>
                            <div class="mb-3">
                                <label for="studentId" class="form-label">学号</label>
                                <input type="text" class="form-control" id="studentId" placeholder="留空自动生成">
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentSchool" class="form-label">学校 *</label>
                                        <select class="form-select" id="studentSchool" required onchange="updateGradeOptions()">
                                            <option value="">请选择学校</option>
                                            ${schools.map(school => `
                                                <option value="${school.id}">${school.name}</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentGrade" class="form-label">年级 *</label>
                                        <select class="form-select" id="studentGrade" required onchange="updateClassOptions()">
                                            <option value="">请先选择学校</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="studentClass" class="form-label">班级 *</label>
                                        <select class="form-select" id="studentClass" required>
                                            <option value="">请先选择年级</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitAddStudent()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    contentArea.innerHTML = studentHtml;
}

/**
 * 渲染学生列表（支持分页）
 */
function renderStudentsList(resetPagination = false) {
    console.log('renderStudentsList 被调用:', {
        resetPagination,
        studentsLength: students.length,
        paginationDataLength: studentsPagination.data.length,
        currentPage: studentsPagination.currentPage
    });

    // 只在需要时重新设置分页数据
    if (resetPagination || studentsPagination.data.length !== students.length) {
        console.log('重置学生分页数据');
        studentsPagination.setData(students);
    }

    if (students.length === 0) {
        return `
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>暂无学生数据</h5>
                <p class="text-muted">点击上方"添加学生"按钮开始添加</p>
            </div>
        `;
    }

    // 获取当前页数据
    const currentPageStudents = studentsPagination.getCurrentPageData();

    return `
        <!-- 批量操作工具栏 -->
        <div class="mb-3 d-flex justify-content-between align-items-center">
            <div class="batch-actions" style="display: none;">
                <span class="me-2 text-muted">已选择 <span id="selected-count">0</span> 个学生</span>
                <button class="btn btn-sm btn-danger" onclick="batchDeleteStudents()">
                    <i class="fas fa-trash"></i> 批量删除
                </button>
                <button class="btn btn-sm btn-secondary ms-2" onclick="clearStudentSelection()">
                    <i class="fas fa-times"></i> 取消选择
                </button>
            </div>
            <div class="ms-auto">
                <small class="text-muted">共 ${students.length} 个学生</small>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table" style="background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <thead style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <tr>
                        <th style="padding: 15px; font-weight: 600; width: 50px;">
                            <input type="checkbox" id="selectAllStudents" onchange="toggleAllStudents(this)">
                        </th>
                        <th style="padding: 15px; font-weight: 600;">学号</th>
                        <th style="padding: 15px; font-weight: 600;">姓名</th>
                        <th style="padding: 15px; font-weight: 600;">学校</th>
                        <th style="padding: 15px; font-weight: 600;">年级班级</th>
                        <th style="padding: 15px; font-weight: 600;">组号</th>
                        <th style="padding: 15px; font-weight: 600;">性别</th>
                        <th style="padding: 15px; font-weight: 600;">座位号</th>
                        <th style="padding: 15px; font-weight: 600; text-align: center;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${currentPageStudents.map(student => {
                        const school = schools.find(s => s.id === student.school_id);
                        return `
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td style="padding: 15px;">
                                    <input type="checkbox" class="student-checkbox" value="${student.id}" onchange="updateStudentSelection()">
                                </td>
                                <td style="padding: 15px;">${student.student_identifier || '-'}</td>
                                <td style="padding: 15px;"><strong>${student.name}</strong></td>
                                <td style="padding: 15px;">${school ? school.name : (student.schools?.name || '未知学校')}</td>
                                <td style="padding: 15px;">${student.grade}年级${student.class}班</td>
                                <td style="padding: 15px;">${student.group_number || '-'}</td>
                                <td style="padding: 15px;">${student.gender || '-'}</td>
                                <td style="padding: 15px;">${student.seat_number || '-'}</td>
                                <td style="padding: 15px; text-align: center;">
                                    <button onclick="editStudent(${student.id})" class="btn btn-sm btn-success me-1" title="编辑">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button onclick="deleteStudent(${student.id})" class="btn btn-sm btn-danger" title="删除">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
        ${studentsPagination.renderPaginationControls()}
    `;
}

/**
 * 获取教师任教的学校列表
 */
function getTeacherSchools() {
    const teacherSchools = new Set();
    grades.forEach(grade => {
        teacherSchools.add(grade.school_id);
    });
    return schools.filter(school => teacherSchools.has(school.id));
}

/**
 * 获取教师在指定学校任教的年级班级
 */
function getTeacherGradesForSchool(schoolId) {
    return grades.filter(grade => grade.school_id === parseInt(schoolId));
}

/**
 * 显示添加学生模态框
 */
function showAddStudentModal() {
    const teacherSchools = getTeacherSchools();

    if (teacherSchools.length === 0) {
        showMessage('您还没有被分配任教的学校和班级，请联系管理员', 'warning');
        return;
    }

    const modalHtml = `
        <div class="modal fade" id="addStudentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">添加学生</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学生姓名 *</label>
                                        <input type="text" class="form-control" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学号</label>
                                        <input type="text" class="form-control" name="student_id" placeholder="留空自动生成">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">学校 *</label>
                                        <select class="form-select" name="school_id" required onchange="updateStudentGradeOptions(this.value)">
                                            <option value="">选择学校</option>
                                            ${teacherSchools.map(school => `<option value="${school.id}">${school.name}</option>`).join('')}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">年级 *</label>
                                        <select class="form-select" name="grade" required onchange="updateStudentClassOptions(this.value)">
                                            <option value="">先选择学校</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">班级 *</label>
                                        <select class="form-select" name="class" required>
                                            <option value="">先选择年级</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">组号</label>
                                        <input type="number" class="form-control" name="group_number" min="1" max="10">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">性别</label>
                                        <select class="form-select" name="gender">
                                            <option value="">选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">座位号</label>
                                        <input type="number" class="form-control" name="seat_number" min="1" max="60">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="submitAddStudent()">添加学生</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('addStudentModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('addStudentModal'));

    // 添加模态框事件监听器，修复 aria-hidden 警告
    const modalElement = document.getElementById('addStudentModal');
    modalElement.addEventListener('hide.bs.modal', function () {
        // 移除焦点，避免 aria-hidden 警告
        const focusedElement = modalElement.querySelector(':focus');
        if (focusedElement) {
            focusedElement.blur();
        }
    });

    modal.show();
}

/**
 * 更新年级选项
 */
function updateGradeOptions() {
    const schoolId = document.getElementById('studentSchool').value;
    const gradeSelect = document.getElementById('studentGrade');
    const classSelect = document.getElementById('studentClass');

    // 清空年级和班级选项
    gradeSelect.innerHTML = '<option value="">请选择年级</option>';
    classSelect.innerHTML = '<option value="">请先选择年级</option>';

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">请先选择学校</option>';
        return;
    }

    // 获取该学校的年级
    const schoolGrades = grades.filter(g => g.school_id == schoolId);
    const uniqueGrades = [...new Set(schoolGrades.map(g => g.grade))].sort((a, b) => a - b);

    uniqueGrades.forEach(grade => {
        gradeSelect.innerHTML += `<option value="${grade}">${grade}年级</option>`;
    });
}

/**
 * 更新班级选项
 */
function updateClassOptions() {
    const schoolId = document.getElementById('studentSchool').value;
    const gradeLevel = document.getElementById('studentGrade').value;
    const classSelect = document.getElementById('studentClass');

    // 清空班级选项
    classSelect.innerHTML = '<option value="">请选择班级</option>';

    if (!schoolId || !gradeLevel) {
        classSelect.innerHTML = '<option value="">请先选择学校和年级</option>';
        return;
    }

    // 获取该学校该年级的班级
    const schoolGradeClasses = grades.filter(g =>
        g.school_id == schoolId && g.grade == gradeLevel
    );
    const uniqueClasses = [...new Set(schoolGradeClasses.map(g => g.class))].sort((a, b) => a - b);

    uniqueClasses.forEach(cls => {
        classSelect.innerHTML += `<option value="${cls}">${cls}班</option>`;
    });
}

/**
 * 更新学生添加表单的年级选项
 */
function updateStudentGradeOptions(schoolId) {
    const gradeSelect = document.querySelector('#addStudentForm select[name="grade"]');
    const classSelect = document.querySelector('#addStudentForm select[name="class"]');

    // 清空年级和班级选项
    gradeSelect.innerHTML = '<option value="">选择年级</option>';
    classSelect.innerHTML = '<option value="">先选择年级</option>';

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">先选择学校</option>';
        return;
    }

    // 获取该学校的年级
    const schoolGrades = getTeacherGradesForSchool(schoolId);
    const uniqueGrades = [...new Set(schoolGrades.map(g => g.grade))].sort((a, b) => a - b);

    uniqueGrades.forEach(grade => {
        gradeSelect.innerHTML += `<option value="${grade}">${grade}年级</option>`;
    });
}

/**
 * 更新学生添加表单的班级选项
 */
function updateStudentClassOptions(gradeLevel) {
    const schoolSelect = document.querySelector('#addStudentForm select[name="school_id"]');
    const classSelect = document.querySelector('#addStudentForm select[name="class"]');

    classSelect.innerHTML = '<option value="">选择班级</option>';

    const schoolId = schoolSelect.value;
    if (!schoolId || !gradeLevel) {
        classSelect.innerHTML = '<option value="">请先选择学校和年级</option>';
        return;
    }

    // 获取该学校该年级的班级
    const schoolGradeClasses = grades.filter(g =>
        g.school_id == schoolId && g.grade == gradeLevel
    );
    const uniqueClasses = [...new Set(schoolGradeClasses.map(g => g.class))].sort((a, b) => a - b);

    uniqueClasses.forEach(cls => {
        classSelect.innerHTML += `<option value="${cls}">${cls}班</option>`;
    });
}

/**
 * 提交添加学生
 */
async function submitAddStudent() {
    try {
        const form = document.getElementById('addStudentForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // 调试：打印表单数据
        console.log('添加学生表单数据:', data);

        // 验证必填字段
        if (!data.name || !data.school_id || !data.grade || !data.class) {
            showMessage('请填写所有必填字段', 'error');
            return;
        }

        // 确保数据类型正确
        const studentData = {
            name: data.name.trim(),
            school_id: parseInt(data.school_id),
            grade: parseInt(data.grade),
            class: parseInt(data.class),  // 后端会解构为 className
            student_id: data.student_id ? data.student_id.trim() : undefined,
            group_number: data.group_number ? parseInt(data.group_number) : undefined,
            gender: data.gender || undefined,
            seat_number: data.seat_number ? parseInt(data.seat_number) : undefined
        };

        // 调试：打印处理后的数据
        console.log('处理后的学生数据:', studentData);

        const response = await apiRequest('/api/teacher/students', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        console.log('添加学生响应:', response);
        showMessage('学生添加成功', 'success');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addStudentModal'));
        if (modal) {
            modal.hide();
        }

        // 静默刷新学生列表
        students = await loadStudents();
        updateStudentsList();

    } catch (error) {
        console.error('添加学生失败:', error);

        // 显示具体的错误信息
        let errorMessage = '添加学生失败';
        if (error.message) {
            errorMessage += ': ' + error.message;
        }

        // 如果是权限问题，提供更友好的提示
        if (error.message && error.message.includes('无权限')) {
            errorMessage = '无权限在该班级添加学生。请先确保您有该班级的管理权限。';
        } else if (error.message && error.message.includes('学号已存在')) {
            errorMessage = '学号已存在，请使用其他学号或留空自动生成。';
        }

        showMessage(errorMessage, 'error');
    }
}

/**
 * 筛选学生
 */
function filterStudents() {
    const searchTerm = document.getElementById('student-search').value.toLowerCase();
    const schoolFilter = document.getElementById('student-school-filter').value;
    const gradeFilter = document.getElementById('student-grade-filter').value;

    let filteredStudents = students;

    if (searchTerm) {
        filteredStudents = filteredStudents.filter(student =>
            student.name.toLowerCase().includes(searchTerm)
        );
    }

    if (schoolFilter) {
        filteredStudents = filteredStudents.filter(student => student.school_id == schoolFilter);
    }

    if (gradeFilter) {
        filteredStudents = filteredStudents.filter(student => student.grade == gradeFilter);
    }

    const listContainer = document.getElementById('students-list');
    if (listContainer) {
        // 临时替换students数组进行渲染
        const originalStudents = students;
        students = filteredStudents;

        // 重置分页并更新显示
        studentsPagination.setData(filteredStudents);
        listContainer.innerHTML = renderStudentsList();

        students = originalStudents;
    }
}



/**
 * 删除学生
 */
async function deleteStudent(studentId) {
    const student = students.find(s => s.id === studentId);
    if (!student) return;

    if (!confirm(`确定要删除学生"${student.name}"吗？`)) return;

    try {
        await apiRequest(`/api/teacher/students/${studentId}`, {
            method: 'DELETE'
        });

        showMessage('学生删除成功', 'success');

        // 重新加载数据
        students = await loadStudents();
        showStudentManagement();

    } catch (error) {
        showMessage('删除学生失败', 'error');
    }
}

// 导出到全局
window.showSection = showSection;
window.showMessage = showMessage;
window.refreshData = refreshData;
window.changePaginationSize = changePaginationSize;
window.changePaginationPage = changePaginationPage;
window.updateSchoolsList = updateSchoolsList;
window.updateGradesList = updateGradesList;
window.updateStudentsList = updateStudentsList;
window.showAddSchoolModal = showAddSchoolModal;
window.submitAddSchool = submitAddSchool;
window.filterSchools = filterSchools;
window.editSchool = editSchool;
window.submitEditSchool = submitEditSchool;
window.deleteSchool = deleteSchool;
window.showAddGradeModal = showAddGradeModal;
window.updateClassCountOptions = updateClassCountOptions;
window.generateClassOptions = generateClassOptions;
window.selectAllClasses = selectAllClasses;
window.clearAllClasses = clearAllClasses;
// ==================== 新增学生管理功能 ====================

/**
 * 编辑学生信息
 */
async function editStudent(studentId) {
    try {
        // 获取学生信息
        const student = students.find(s => s.id === studentId);
        if (!student) {
            showMessage('学生信息不存在', 'error');
            return;
        }

        const teacherSchools = getTeacherSchools();

        if (teacherSchools.length === 0) {
            showMessage('您还没有被分配任教的学校和班级，请联系管理员', 'warning');
            return;
        }

        // 检查学生是否在教师任教的学校中
        const studentSchool = teacherSchools.find(school => school.id === student.school_id);
        if (!studentSchool) {
            showMessage('您无权编辑该学生信息（不在您任教的学校中）', 'warning');
            return;
        }

        // 获取该学校的年级班级信息
        const schoolGrades = getTeacherGradesForSchool(student.school_id);
        const availableGrades = [...new Set(schoolGrades.map(g => g.grade))].sort((a, b) => a - b);
        const availableClasses = schoolGrades.filter(g => g.grade === student.grade).map(g => g.class).sort((a, b) => a - b);

        const modalHtml = `
            <div class="modal fade" id="editStudentModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑学生信息</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editStudentForm">
                                <input type="hidden" name="id" value="${student.id}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">学生姓名 *</label>
                                            <input type="text" class="form-control" name="name" value="${student.name}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">学号</label>
                                            <input type="text" class="form-control" value="${student.student_identifier}" readonly>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">学校 *</label>
                                            <select class="form-select" name="school_id" required onchange="updateEditStudentGradeOptions(this.value, ${student.grade}, ${student.class})">
                                                ${teacherSchools.map(school =>
                                                    `<option value="${school.id}" ${school.id === student.school_id ? 'selected' : ''}>${school.name}</option>`
                                                ).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">年级 *</label>
                                            <select class="form-select" name="grade" required onchange="updateEditStudentClassOptions(this.value, ${student.class})">
                                                ${availableGrades.map(grade =>
                                                    `<option value="${grade}" ${grade === student.grade ? 'selected' : ''}>${grade}年级</option>`
                                                ).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">班级 *</label>
                                            <select class="form-select" name="class" required>
                                                ${availableClasses.map(cls =>
                                                    `<option value="${cls}" ${cls === student.class ? 'selected' : ''}>${cls}班</option>`
                                                ).join('')}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">组号</label>
                                            <input type="number" class="form-control" name="group_number" value="${student.group_number || ''}" min="1" max="10">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">性别</label>
                                            <select class="form-select" name="gender">
                                                <option value="">选择性别</option>
                                                <option value="男" ${student.gender === '男' ? 'selected' : ''}>男</option>
                                                <option value="女" ${student.gender === '女' ? 'selected' : ''}>女</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">座位号</label>
                                            <input type="number" class="form-control" name="seat_number" value="${student.seat_number || ''}" min="1" max="60">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="submitEditStudent()">保存修改</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('editStudentModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editStudentModal'));

        // 添加模态框事件监听器，修复 aria-hidden 警告
        const modalElement = document.getElementById('editStudentModal');
        modalElement.addEventListener('hide.bs.modal', function () {
            // 移除焦点，避免 aria-hidden 警告
            const focusedElement = modalElement.querySelector(':focus');
            if (focusedElement) {
                focusedElement.blur();
            }
        });

        modal.show();

    } catch (error) {
        console.error('编辑学生失败:', error);
        showMessage('编辑学生失败: ' + error.message, 'error');
    }
}

/**
 * 更新编辑学生表单的年级选项
 */
function updateEditStudentGradeOptions(schoolId, currentGrade = null, currentClass = null) {
    const gradeSelect = document.querySelector('#editStudentForm select[name="grade"]');
    const classSelect = document.querySelector('#editStudentForm select[name="class"]');

    // 清空年级和班级选项
    gradeSelect.innerHTML = '<option value="">选择年级</option>';
    classSelect.innerHTML = '<option value="">先选择年级</option>';

    if (!schoolId) {
        gradeSelect.innerHTML = '<option value="">先选择学校</option>';
        return;
    }

    // 获取该学校的年级
    const schoolGrades = getTeacherGradesForSchool(schoolId);
    const uniqueGrades = [...new Set(schoolGrades.map(g => g.grade))].sort((a, b) => a - b);

    uniqueGrades.forEach(grade => {
        const selected = grade === currentGrade ? 'selected' : '';
        gradeSelect.innerHTML += `<option value="${grade}" ${selected}>${grade}年级</option>`;
    });

    // 如果有当前年级，更新班级选项
    if (currentGrade) {
        updateEditStudentClassOptions(currentGrade, currentClass);
    }
}

/**
 * 更新编辑学生表单的班级选项
 */
function updateEditStudentClassOptions(gradeLevel, currentClass = null) {
    const schoolSelect = document.querySelector('#editStudentForm select[name="school_id"]');
    const classSelect = document.querySelector('#editStudentForm select[name="class"]');

    classSelect.innerHTML = '<option value="">选择班级</option>';

    const schoolId = schoolSelect.value;
    if (!schoolId || !gradeLevel) {
        classSelect.innerHTML = '<option value="">请先选择学校和年级</option>';
        return;
    }

    // 获取该学校该年级的班级
    const schoolGradeClasses = grades.filter(g =>
        g.school_id == schoolId && g.grade == gradeLevel
    );
    const uniqueClasses = [...new Set(schoolGradeClasses.map(g => g.class))].sort((a, b) => a - b);

    uniqueClasses.forEach(cls => {
        const selected = cls === currentClass ? 'selected' : '';
        classSelect.innerHTML += `<option value="${cls}" ${selected}>${cls}班</option>`;
    });
}

/**
 * 提交编辑学生表单
 */
async function submitEditStudent() {
    try {
        const form = document.getElementById('editStudentForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // 调试：打印表单数据
        console.log('编辑学生表单数据:', data);

        // 验证必填字段
        if (!data.name || !data.school_id || !data.grade || !data.class) {
            showMessage('请填写所有必填字段', 'error');
            return;
        }

        // 确保数据类型正确
        const studentData = {
            id: parseInt(data.id),
            name: data.name.trim(),
            school_id: parseInt(data.school_id),
            grade: parseInt(data.grade),
            class: parseInt(data.class),  // 后端会解构为 className
            group_number: data.group_number ? parseInt(data.group_number) : undefined,
            gender: data.gender || undefined,
            seat_number: data.seat_number ? parseInt(data.seat_number) : undefined
        };

        // 调试：打印处理后的数据
        console.log('处理后的学生数据:', studentData);

        const response = await apiRequest(`/api/teacher/students/${data.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        showMessage('学生信息更新成功', 'success');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editStudentModal'));
        modal.hide();

        // 静默刷新学生列表
        students = await loadStudents();
        updateStudentsList();

    } catch (error) {
        console.error('更新学生信息失败:', error);
        showMessage('更新学生信息失败: ' + error.message, 'error');
    }
}

/**
 * 删除学生
 */
async function deleteStudent(studentId) {
    try {
        const student = students.find(s => s.id === studentId);
        if (!student) {
            showMessage('学生信息不存在', 'error');
            return;
        }

        if (!confirm(`确定要删除学生"${student.name}"吗？\n\n⚠️ 警告：这将永久删除学生记录，无法恢复！`)) {
            return;
        }

        await apiRequest(`/api/teacher/students/${studentId}`, {
            method: 'DELETE'
        });

        showMessage('学生删除成功', 'success');

        // 静默刷新学生列表
        students = await loadStudents();
        updateStudentsList();

    } catch (error) {
        console.error('删除学生失败:', error);
        showMessage('删除学生失败: ' + error.message, 'error');
    }
}

window.submitAddGrade = submitAddGrade;
window.filterGrades = filterGrades;
window.deleteGrade = deleteGrade;
window.toggleAllGrades = toggleAllGrades;
window.batchDeleteGrades = batchDeleteGrades;
window.batchUpgradeGrades = batchUpgradeGrades;
window.showAddStudentModal = showAddStudentModal;
window.updateGradeOptions = updateGradeOptions;
window.updateClassOptions = updateClassOptions;
window.submitAddStudent = submitAddStudent;
window.filterStudents = filterStudents;
window.editStudent = editStudent;
window.deleteStudent = deleteStudent;
/**
 * 显示批量导入模态框
 */
function showBatchImportModal() {
    const modalHtml = `
        <div class="modal fade" id="batchImportModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">批量导入学生</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> 导入说明</h6>
                            <ul class="mb-0">
                                <li>支持Excel文件格式（.xlsx, .xls）</li>
                                <li>必填列：姓名、学校、年级、班级</li>
                                <li>可选列：学号、组号、性别、座位号</li>
                                <li>如果学校不存在，系统会自动创建</li>
                                <li>教师会自动获得导入班级的管理权限</li>
                            </ul>
                            <div class="mt-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="downloadTemplate()">
                                    <i class="fas fa-download"></i> 下载Excel模板
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="viewTemplateGuide()">
                                    <i class="fas fa-question-circle"></i> 查看详细说明
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">选择Excel文件</label>
                            <input type="file" class="form-control" id="excelFile" accept=".xlsx,.xls" onchange="previewExcelFile()">
                            <div class="form-text">文件大小限制：10MB</div>
                        </div>

                        <!-- 文件预览区域 -->
                        <div id="filePreview" class="d-none mb-3">
                            <h6><i class="fas fa-table"></i> 文件预览</h6>
                            <div class="alert alert-success">
                                <div id="fileInfo"></div>
                            </div>
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table class="table table-bordered table-sm" id="previewTable">
                                    <thead class="table-light sticky-top"></thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>

                        <div class="mb-3" id="formatExample">
                            <h6>Excel表格格式示例：</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>姓名*</th>
                                            <th>学校*</th>
                                            <th>年级*</th>
                                            <th>班级*</th>
                                            <th>学号</th>
                                            <th>组号</th>
                                            <th>性别</th>
                                            <th>座位号</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>张三</td>
                                            <td>实验小学</td>
                                            <td>5</td>
                                            <td>1</td>
                                            <td>20240001</td>
                                            <td>1</td>
                                            <td>男</td>
                                            <td>1</td>
                                        </tr>
                                        <tr>
                                            <td>李四</td>
                                            <td>实验小学</td>
                                            <td>5</td>
                                            <td>1</td>
                                            <td>20240002</td>
                                            <td>2</td>
                                            <td>女</td>
                                            <td>2</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 导入进度条 -->
                        <div id="importProgress" class="d-none">
                            <h6><i class="fas fa-spinner fa-spin"></i> 正在导入...</h6>
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 0%" id="progressBar">
                                    <span id="progressText">0%</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div id="importStatus" class="text-muted">准备导入...</div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small id="importStats" class="text-muted">等待开始...</small>
                                </div>
                            </div>
                        </div>

                        <div id="importResults" class="d-none">
                            <h6>导入结果：</h6>
                            <div id="importSummary"></div>
                            <div id="importErrors" class="mt-3"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="startBatchImport()" id="importBtn">
                            <i class="fas fa-upload"></i> 开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('batchImportModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchImportModal'));

    // 添加模态框事件监听器，修复 aria-hidden 警告
    const modalElement = document.getElementById('batchImportModal');
    modalElement.addEventListener('hide.bs.modal', function () {
        // 移除焦点，避免 aria-hidden 警告
        const focusedElement = modalElement.querySelector(':focus');
        if (focusedElement) {
            focusedElement.blur();
        }
    });

    modal.show();
}

/**
 * 预览Excel文件
 */
async function previewExcelFile() {
    const fileInput = document.getElementById('excelFile');
    const file = fileInput.files[0];

    if (!file) {
        document.getElementById('filePreview').classList.add('d-none');
        document.getElementById('formatExample').classList.remove('d-none');
        return;
    }

    // 检查XLSX库是否已加载
    if (typeof XLSX === 'undefined') {
        showMessage('Excel处理库未加载，请刷新页面重试', 'error');
        return;
    }

    try {
        // 显示文件信息
        const fileInfo = document.getElementById('fileInfo');
        fileInfo.innerHTML = `
            <strong>文件名：</strong>${file.name}<br>
            <strong>文件大小：</strong>${(file.size / 1024 / 1024).toFixed(2)} MB<br>
            <strong>文件类型：</strong>${file.type || '未知'}
        `;

        // 读取Excel文件
        const data = await file.arrayBuffer();
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (jsonData.length === 0) {
            throw new Error('Excel文件为空');
        }

        // 显示预览表格
        const previewTable = document.getElementById('previewTable');
        const thead = previewTable.querySelector('thead');
        const tbody = previewTable.querySelector('tbody');

        // 清空表格
        thead.innerHTML = '';
        tbody.innerHTML = '';

        // 添加表头
        if (jsonData.length > 0) {
            const headerRow = document.createElement('tr');
            jsonData[0].forEach((header, index) => {
                const th = document.createElement('th');
                th.textContent = header || `列${index + 1}`;
                th.style.minWidth = '100px';
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
        }

        // 添加数据行（最多显示10行）
        const maxRows = Math.min(jsonData.length - 1, 10);
        for (let i = 1; i <= maxRows; i++) {
            const row = jsonData[i];
            const tr = document.createElement('tr');

            jsonData[0].forEach((_, colIndex) => {
                const td = document.createElement('td');
                td.textContent = row[colIndex] || '';
                td.style.maxWidth = '150px';
                td.style.overflow = 'hidden';
                td.style.textOverflow = 'ellipsis';
                td.style.whiteSpace = 'nowrap';
                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        }

        // 更新文件信息
        fileInfo.innerHTML += `<br><strong>数据行数：</strong>${jsonData.length - 1} 行（不含表头）`;
        if (jsonData.length > 11) {
            fileInfo.innerHTML += `<br><small class="text-muted">预览仅显示前10行数据</small>`;
        }

        // 显示预览，隐藏示例
        document.getElementById('filePreview').classList.remove('d-none');
        document.getElementById('formatExample').classList.add('d-none');

    } catch (error) {
        console.error('预览Excel文件失败:', error);
        showMessage('预览Excel文件失败: ' + error.message, 'error');
        document.getElementById('filePreview').classList.add('d-none');
        document.getElementById('formatExample').classList.remove('d-none');
    }
}

/**
 * 更新导入进度
 */
function updateImportProgress(percentage, status, stats) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const importStatus = document.getElementById('importStatus');
    const importStats = document.getElementById('importStats');

    if (progressBar) {
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
    }

    if (progressText) {
        progressText.textContent = percentage + '%';
    }

    if (importStatus) {
        importStatus.textContent = status;
    }

    if (importStats) {
        importStats.textContent = stats;
    }
}

/**
 * 开始批量导入
 */
async function startBatchImport() {
    try {
        const fileInput = document.getElementById('excelFile');
        const file = fileInput.files[0];

        if (!file) {
            showMessage('请选择Excel文件', 'error');
            return;
        }

        // 验证文件类型
        const allowedTypes = ['.xlsx', '.xls'];
        const fileExt = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        if (!allowedTypes.includes(fileExt)) {
            showMessage('只支持Excel文件格式(.xlsx, .xls)', 'error');
            return;
        }

        // 验证文件大小
        if (file.size > 10 * 1024 * 1024) {
            showMessage('文件大小不能超过10MB', 'error');
            return;
        }

        // 显示进度条
        document.getElementById('importProgress').classList.remove('d-none');
        document.getElementById('importResults').classList.add('d-none');
        document.getElementById('importBtn').disabled = true;

        // 更新进度条
        updateImportProgress(10, '正在上传文件...', '准备处理');

        // 创建FormData
        const formData = new FormData();
        formData.append('excel', file);

        updateImportProgress(30, '正在上传到服务器...', '文件准备完成');

        // 发送请求
        const response = await fetch(buildApiUrl('/api/teacher/students/batch-import'), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: formData
        });

        console.log('服务器响应状态:', response.status, response.statusText);
        updateImportProgress(50, '服务器处理中...', '请求已发送');

        if (!response.ok) {
            const errorText = await response.text();
            console.error('服务器错误响应:', errorText);
            throw new Error(`服务器错误 ${response.status}: ${errorText}`);
        }

        updateImportProgress(70, '正在解析服务器响应...', '服务器处理完成');

        const result = await response.json();

        console.log('批量导入响应:', result); // 添加调试日志

        if (!response.ok) {
            throw new Error(result.error || result.message || '导入失败');
        }

        // 检查响应数据结构
        if (!result || !result.data) {
            throw new Error('服务器响应数据格式错误');
        }

        updateImportProgress(90, '正在更新界面...', '数据处理完成');

        // 显示结果
        displayImportResults(result.data);

        const successCount = result.data.success || 0;
        const failedCount = result.data.failed || 0;

        updateImportProgress(100, '导入完成！', `成功: ${successCount}, 失败: ${failedCount}`);

        // 刷新数据
        students = await loadStudents();
        schools = await loadSchools();
        grades = await loadGrades();

        // 自动关闭模态框并刷新页面（带倒计时）
        if (successCount > 0) {
            let countdown = 3;
            window.cancelAutoCloseFlag = false;

            const countdownInterval = setInterval(() => {
                const countdownElement = document.getElementById('autoCloseCountdown');
                if (countdownElement && !window.cancelAutoCloseFlag) {
                    countdown--;
                    countdownElement.textContent = countdown;

                    if (countdown <= 0) {
                        clearInterval(countdownInterval);

                        if (!window.cancelAutoCloseFlag) {
                            const modal = bootstrap.Modal.getInstance(document.getElementById('batchImportModal'));
                            if (modal) {
                                modal.hide();
                            }

                            // 刷新当前页面
                            if (currentSection === 'students') {
                                showStudentManagement();
                            }

                            const message = result.message || `导入完成：成功 ${successCount} 个，失败 ${failedCount} 个`;
                            showMessage(message, failedCount > 0 ? 'warning' : 'success');
                        }
                    }
                } else {
                    clearInterval(countdownInterval);
                }
            }, 1000);
        } else {
            // 如果没有成功导入任何数据，不自动关闭
            console.log('没有成功导入的数据，不自动关闭模态框');
        }

        // 备用自动关闭机制（10秒后强制关闭，防止界面卡住）
        setTimeout(() => {
            if (!window.cancelAutoCloseFlag) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('batchImportModal'));
                if (modal) {
                    console.log('备用机制：10秒后自动关闭模态框');
                    modal.hide();
                }

                // 刷新当前页面
                if (currentSection === 'students') {
                    showStudentManagement();
                }
            }
        }, 10000);

    } catch (error) {
        console.error('批量导入失败:', error);

        // 更新进度条显示错误状态
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const importStatus = document.getElementById('importStatus');

        if (progressBar) {
            progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
            progressBar.classList.add('bg-danger');
            progressBar.style.width = '100%';
        }

        if (progressText) {
            progressText.textContent = '失败';
        }

        if (importStatus) {
            importStatus.textContent = '导入失败: ' + error.message;
        }

        showMessage('批量导入失败: ' + error.message, 'error');

        // 3秒后隐藏进度条
        setTimeout(() => {
            document.getElementById('importProgress').classList.add('d-none');
        }, 3000);

    } finally {
        document.getElementById('importBtn').disabled = false;
    }
}

/**
 * 显示导入结果
 */
function displayImportResults(results) {
    const resultsDiv = document.getElementById('importResults');
    const summaryDiv = document.getElementById('importSummary');
    const errorsDiv = document.getElementById('importErrors');

    // 确定整体状态
    const hasErrors = results.failed > 0;
    const hasSkipped = results.skipped > 0;
    const alertClass = hasErrors ? 'alert-warning' : (hasSkipped ? 'alert-info' : 'alert-success');

    // 显示详细摘要
    summaryDiv.innerHTML = `
        <div class="alert ${alertClass}">
            <h6><i class="fas fa-chart-bar"></i> 导入结果统计</h6>
            <div class="row">
                <div class="col-md-6">
                    <strong>处理结果：</strong><br>
                    <span class="text-success">✓ 成功导入：${results.success} 个</span><br>
                    ${hasSkipped ? `<span class="text-info">⚠ 跳过重复：${results.skipped} 个</span><br>` : ''}
                    ${hasErrors ? `<span class="text-danger">✗ 导入失败：${results.failed} 个</span><br>` : ''}
                    <small class="text-muted">总计处理：${results.total_processed || (results.success + results.failed + (results.skipped || 0))} 条记录</small>
                </div>
                <div class="col-md-6">
                    <strong>自动创建：</strong><br>
                    ${results.created_schools && results.created_schools.length > 0 ?
                        `<span class="text-primary">🏫 新建学校：${results.created_schools.length} 个</span><br>` : ''}
                    ${results.created_permissions && results.created_permissions.length > 0 ?
                        `<span class="text-primary">🔑 分配权限：${results.created_permissions.length} 个班级</span><br>` : ''}
                    ${results.processed_classes && results.processed_classes.length > 0 ?
                        `<span class="text-info">📚 涉及班级：${results.processed_classes.length} 个</span>` : ''}
                </div>
            </div>
        </div>
    `;

    // 显示详细信息
    let detailsHtml = '';

    // 显示重复学生信息
    if (results.duplicate_students && results.duplicate_students.length > 0) {
        detailsHtml += `
            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-info-circle"></i> 跳过的重复学生 (${results.duplicate_students.length}个)</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>行号</th>
                                <th>学号</th>
                                <th>姓名</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.duplicate_students.map(dup => `
                                <tr>
                                    <td>${dup.row}</td>
                                    <td>${dup.student_identifier}</td>
                                    <td>${dup.name}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // 显示创建的学校信息
    if (results.created_schools && results.created_schools.length > 0) {
        detailsHtml += `
            <div class="alert alert-success mt-3">
                <h6><i class="fas fa-school"></i> 自动创建的学校 (${results.created_schools.length}个)</h6>
                <ul class="mb-0">
                    ${results.created_schools.map(school => `
                        <li>${school.name} (ID: ${school.id})</li>
                    `).join('')}
                </ul>
            </div>
        `;
    }

    // 显示错误详情
    if (results.errors && results.errors.length > 0) {
        detailsHtml += `
            <div class="alert alert-danger mt-3">
                <h6><i class="fas fa-exclamation-triangle"></i> 导入失败详情 (${results.errors.length}个)</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>行号</th>
                                <th>错误信息</th>
                                <th>原始数据</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.errors.map(error => `
                                <tr>
                                    <td>${error.row}</td>
                                    <td><span class="text-danger">${error.error}</span></td>
                                    <td><small class="text-muted">${JSON.stringify(error.data)}</small></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    errorsDiv.innerHTML = detailsHtml;
    resultsDiv.classList.remove('d-none');

    // 添加自动关闭提示
    if (results.success > 0) {
        const autoCloseDiv = document.createElement('div');
        autoCloseDiv.className = 'alert alert-info mt-3';
        autoCloseDiv.innerHTML = `
            <i class="fas fa-info-circle"></i>
            <span id="autoCloseCountdown">3</span> 秒后自动关闭对话框并刷新页面...
            <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="cancelAutoClose()">
                取消自动关闭
            </button>
        `;
        errorsDiv.appendChild(autoCloseDiv);
    }
}

/**
 * 取消自动关闭
 */
function cancelAutoClose() {
    // 这个函数会在startBatchImport中的setTimeout被调用时检查
    window.cancelAutoCloseFlag = true;
    const autoCloseDiv = document.querySelector('.alert.alert-info');
    if (autoCloseDiv && autoCloseDiv.textContent.includes('秒后自动关闭')) {
        autoCloseDiv.innerHTML = '<i class="fas fa-check"></i> 已取消自动关闭';
    }
}

/**
 * 下载Excel导入模板
 */
function downloadTemplate() {
    // 创建Excel数据
    const data = [
        ['姓名', '学校', '年级', '班级', '学号', '组号', '性别', '座位号'],
        ['张三', '实验小学', 5, 1, '20240001', 1, '男', 1],
        ['李四', '实验小学', 5, 1, '20240002', 2, '女', 2],
        ['王五', '希望中学', 8, 3, '', 3, '男', 15]
    ];

    // 创建CSV内容
    const csvContent = data.map(row =>
        row.map(cell => `"${cell}"`).join(',')
    ).join('\n');

    // 添加BOM以支持中文
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '学生导入模板.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('模板已下载！请用Excel打开并保存为.xlsx格式', 'success');
}

/**
 * 查看模板详细说明
 */
function viewTemplateGuide() {
    window.open('/templates/student_import_template.html', '_blank');
}

/**
 * 全选/取消全选学生
 */
function toggleAllStudents(checkbox) {
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    studentCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateStudentSelection();
}

/**
 * 更新学生选择状态
 */
function updateStudentSelection() {
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const selectAllCheckbox = document.getElementById('selectAllStudents');
    const batchActions = document.querySelector('.batch-actions');
    const selectedCount = document.getElementById('selected-count');

    // 检查关键元素是否存在
    if (!selectAllCheckbox || !batchActions || !selectedCount) {
        return; // 如果关键元素不存在，直接返回
    }

    // 更新全选复选框状态
    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === studentCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // 显示/隐藏批量操作工具栏
    if (checkedBoxes.length > 0) {
        batchActions.style.display = 'block';
        selectedCount.textContent = checkedBoxes.length;
    } else {
        batchActions.style.display = 'none';
    }
}

/**
 * 清除学生选择
 */
function clearStudentSelection() {
    const studentCheckboxes = document.querySelectorAll('.student-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllStudents');

    studentCheckboxes.forEach(cb => {
        if (cb) {
            cb.checked = false;
        }
    });

    // 检查元素是否存在再操作
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }

    updateStudentSelection();
}

/**
 * 批量删除学生
 */
async function batchDeleteStudents() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    const selectedIds = Array.from(checkedBoxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        showMessage('请先选择要删除的学生', 'warning');
        return;
    }

    const selectedStudents = students.filter(s => selectedIds.includes(s.id.toString()));
    const studentNames = selectedStudents.map(s => s.name).join('、');

    if (!confirm(`确定要删除以下 ${selectedIds.length} 个学生吗？\n\n${studentNames}\n\n⚠️ 警告：这将永久删除学生记录，无法恢复！`)) {
        return;
    }

    try {
        showMessage('正在批量删除学生...', 'info');

        // 并发删除，但限制并发数量
        const batchSize = 5;
        const results = [];

        for (let i = 0; i < selectedIds.length; i += batchSize) {
            const batch = selectedIds.slice(i, i + batchSize);
            const batchPromises = batch.map(async (id) => {
                try {
                    const response = await fetch(buildApiUrl(`/api/teacher/students/${id}`), {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || '删除失败');
                    }

                    return { id, success: true };
                } catch (error) {
                    console.error(`删除学生 ${id} 失败:`, error);
                    return { id, success: false, error: error.message };
                }
            });

            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }

        // 统计结果
        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        if (successCount > 0) {
            // 重新加载学生数据
            students = await loadStudents();

            // 完全重新渲染学生管理页面
            showStudentManagement();

            if (failCount === 0) {
                showMessage(`成功删除 ${successCount} 个学生`, 'success');
            } else {
                showMessage(`成功删除 ${successCount} 个学生，${failCount} 个删除失败`, 'warning');
            }
        } else {
            showMessage('批量删除失败', 'error');
        }

    } catch (error) {
        console.error('批量删除学生失败:', error);
        showMessage('批量删除失败: ' + error.message, 'error');
    }
}

window.submitEditStudent = submitEditStudent;
window.showBatchImportModal = showBatchImportModal;
window.previewExcelFile = previewExcelFile;
window.startBatchImport = startBatchImport;
window.cancelAutoClose = cancelAutoClose;
window.downloadTemplate = downloadTemplate;
window.viewTemplateGuide = viewTemplateGuide;
window.toggleAllStudents = toggleAllStudents;
window.updateStudentSelection = updateStudentSelection;
window.clearStudentSelection = clearStudentSelection;
window.batchDeleteStudents = batchDeleteStudents;
window.updateStudentGradeOptions = updateStudentGradeOptions;
window.updateStudentClassOptions = updateStudentClassOptions;
window.updateEditStudentGradeOptions = updateEditStudentGradeOptions;
window.updateEditStudentClassOptions = updateEditStudentClassOptions;
