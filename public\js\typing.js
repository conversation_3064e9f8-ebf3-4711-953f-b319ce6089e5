/**
 * 打字管理模块
 * 管理学生打字成绩数据的显示、编辑和统计功能
 */
const TypingModule = {
    // 打字数据
    data: [],
    // 筛选条件
    filter: {
        school: '',
        grade: '',
        class: '',
        search: ''
    },
    // 排行类型
    rankingType: 'individual',
    // 图表实例
    chart: null,
    // 进步数据
    progressData: [],
    
    /**
     * 初始化打字模块
     */
    init: function() {
        // 设置初始的排行类型属性
        document.body.setAttribute('data-ranking-type', this.rankingType);
        
        // 加载数据
        this.loadData();
        // 绑定事件
        this.bindEvents();
        // 初始化文章相关功能
        this.initArticleFeatures();
    },
    
    /**
     * 加载打字数据
     */
    loadData: function() {
        Utils.showLoading(true);

        console.log('加载所有打字数据');

        // 使用数据库API获取所有最佳打字记录（不使用筛选条件）
        DB.getBestTypingRecords({})
            .then(data => {
                // 确保数据是有效的数组
                if (!data) data = [];
                
                console.log(`获取到${data.length}条学生记录`);
                
                // 确保每个学生对象都有best_speed和best_accuracy属性，即使为0
                this.data = data.map(student => ({
                    ...student,
                    best_speed: student.best_speed || 0,
                    best_accuracy: student.best_accuracy || 0
                }));
                
                // 更新筛选器
                this.updateFilters();
                // 渲染数据
                this.renderData();
                // 获取进步数据
                this.getProgressData();
                // 更新排行榜
                this.updateRanking();
                
                Utils.showLoading(false);
            })
            .catch(error => {
                console.error('加载打字数据失败:', error);
                Utils.showMessage('加载数据失败，请刷新重试');
                Utils.showLoading(false);
            });
    },
    
    /**
     * 获取学生打字进步数据
     */
    getProgressData: function() {
        Utils.showLoading(true);
        
        // 检查是否有学生数据
        if (!this.data || this.data.length === 0) {
            console.log('没有学生数据，无法获取进步数据');
            Utils.showLoading(false);
            return;
        }
        
        // 使用Promise.all同时获取所有学生的历史记录
        const promises = this.data.map(student => {
            // 确保student_identifier存在
            if (!student.student_identifier) {
                console.error('学生缺少identifier:', student);
                return Promise.resolve(null);
            }
            
            return DB.getTypingRecords({
                student_identifier: student.student_identifier
            })
            .then(records => {
                // 如果没有至少两条记录，则无法计算进步
                if (!records || !Array.isArray(records) || records.length < 2) {
                    return null;
                }
                
                try {
                    // 按日期排序（由新到旧）
                    records.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    
                    // 只考虑最新的两条记录
                    const latestRecord = records[0];
                    const previousRecord = records[1];
                    
                    // 确保记录有speed属性
                    if (typeof latestRecord.speed !== 'number' || typeof previousRecord.speed !== 'number') {
                        console.warn('打字记录缺少速度数据:', student.name);
                        return null;
                    }
                    
                    // 计算进步值
                    const progress = latestRecord.speed - previousRecord.speed;
                    
                    return {
                        student_identifier: student.student_identifier,
                        grade: student.grade,
                        class: student.class,
                        name: student.name,
                        latest_speed: latestRecord.speed,
                        previous_speed: previousRecord.speed,
                        progress: progress,
                        progress_date: latestRecord.created_at
                    };
                } catch (error) {
                    console.error(`处理学生${student.name}的历史记录时出错:`, error);
                    return null;
                }
            })
            .catch(error => {
                console.error(`获取学生${student.name}的历史记录失败:`, error);
                return null;
            });
        });
        
        Promise.all(promises)
            .then(results => {
                // 过滤掉null值，并按进步值从大到小排序，只保留进步值为正的学生
                this.progressData = results
                    .filter(item => item !== null)
                    .filter(item => item.progress > 0) // 只保留进步值为正的学生
                    .sort((a, b) => b.progress - a.progress);
                
                console.log(`获取到${this.progressData.length}条进步数据`);
                Utils.showLoading(false);
                
                // 如果当前排行类型是进步之星，则更新排行榜
                if (this.rankingType === 'progress') {
                    this.updateProgressRanking();
                }
            })
            .catch(error => {
                console.error('处理进步数据失败:', error);
                this.progressData = []; // 确保即使失败也有一个空数组
                Utils.showLoading(false);
                
                // 如果当前排行类型是进步之星，显示错误提示
                if (this.rankingType === 'progress') {
                    const rankingDiv = document.getElementById('typing-ranking');
                    if (rankingDiv) {
                        rankingDiv.innerHTML = '<div class="no-data">获取进步数据失败，请稍后重试</div>';
                    }
                }
            });
    },
    
    /**
     * 绑定事件处理
     */
    bindEvents: function() {
        // 学校筛选
        document.getElementById('typing-school-filter').addEventListener('change', (e) => {
            this.filter.school = e.target.value;
            // 重置下级筛选条件
            this.filter.grade = '';
            this.filter.class = '';
            // 更新筛选器状态
            this.updateGradeFilter();
            this.updateClassFilter();
            this.renderData();
        });

        // 年级筛选
        document.getElementById('typing-grade-filter').addEventListener('change', (e) => {
            this.filter.grade = e.target.value;
            // 重置班级筛选条件
            this.filter.class = '';
            // 更新班级筛选器
            this.updateClassFilter();
            this.renderData();
        });

        // 班级筛选
        document.getElementById('typing-class-filter').addEventListener('change', (e) => {
            this.filter.class = e.target.value;
            this.renderData();
        });

        // 搜索
        document.getElementById('typing-search').addEventListener('input', (e) => {
            this.filter.search = e.target.value.trim().toLowerCase();
            this.renderData();
        });
        
        // 重置筛选
        document.getElementById('typing-reset-filter').addEventListener('click', this.resetFilters.bind(this));
        
        // 导出数据
        document.getElementById('typing-export').addEventListener('click', this.handleExport.bind(this));

        // 刷新数据按钮
        document.getElementById('typing-refresh').addEventListener('click', () => {
            // 刷新数据
            this.loadData();
            // 显示提示信息
            Utils.showMessage('数据已刷新');
        });
        
        // 排行类型选择
        document.getElementById('typing-ranking-type').addEventListener('change', (e) => {
            this.rankingType = e.target.value;
            // 更新body的data-ranking-type属性以便CSS可以控制显示
            document.body.setAttribute('data-ranking-type', this.rankingType);
            this.updateRanking();
        });
        
        // 表格区域事件委托（目前只用于未来扩展）
        document.getElementById('typing-data').addEventListener('click', (e) => {
            // 预留用于未来功能扩展
        });

        // 添加文章按钮
        document.getElementById('add-article-btn').addEventListener('click', this.showAddArticleModal.bind(this));
    },

    

    
    /**
     * 获取排名CSS类名
     * @param {number} index - 排名索引（从0开始）
     * @returns {string} 排名CSS类名
     */
    getRankClass: function(index) {
        return index < 3 ? `rank-${index + 1}` : '';
    },

    /**
     * 更新筛选器选项
     */
    updateFilters: function() {
        // 更新学校筛选器
        const schools = [...new Set(this.data.map(item => item.school_name).filter(Boolean))];
        const schoolFilter = document.getElementById('typing-school-filter');
        Utils.updateSelectOptions(schoolFilter, schools, '全部学校');

        // 初始化年级和班级筛选器为禁用状态
        this.initializeFilterStates();
    },

    /**
     * 初始化筛选器状态
     */
    initializeFilterStates: function() {
        const gradeFilter = document.getElementById('typing-grade-filter');
        const classFilter = document.getElementById('typing-class-filter');

        // 初始状态：年级筛选器禁用，显示提示
        gradeFilter.innerHTML = '<option value="">请先选择学校</option>';
        gradeFilter.disabled = true;

        // 初始状态：班级筛选器禁用，显示提示
        classFilter.innerHTML = '<option value="">请先选择年级</option>';
        classFilter.disabled = true;
    },

    /**
     * 根据选中的学校更新年级筛选器
     */
    updateGradeFilter: function() {
        const gradeFilter = document.getElementById('typing-grade-filter');

        if (!this.filter.school) {
            // 如果没有选择学校，禁用年级筛选器
            gradeFilter.innerHTML = '<option value="">请先选择学校</option>';
            gradeFilter.disabled = true;
            return;
        }

        // 启用年级筛选器
        gradeFilter.disabled = false;

        // 按选中的学校过滤数据
        const filteredData = this.data.filter(item => item.school_name === this.filter.school);
        const { grades } = Utils.getGradesAndClasses(filteredData);

        // 更新年级选项
        Utils.updateSelectOptions(gradeFilter, grades, '全部年级');

        // 重置年级筛选器的值
        gradeFilter.value = this.filter.grade || '';
    },
    
    /**
     * 根据选中的学校和年级更新班级筛选器
     */
    updateClassFilter: function() {
        const classFilter = document.getElementById('typing-class-filter');

        if (!this.filter.school) {
            // 如果没有选择学校，禁用班级筛选器
            classFilter.innerHTML = '<option value="">请先选择学校</option>';
            classFilter.disabled = true;
            return;
        }

        if (!this.filter.grade) {
            // 如果没有选择年级，禁用班级筛选器
            classFilter.innerHTML = '<option value="">请先选择年级</option>';
            classFilter.disabled = true;
            return;
        }

        // 启用班级筛选器
        classFilter.disabled = false;

        // 按选中的学校过滤数据
        const filteredData = this.data.filter(item => item.school_name === this.filter.school);

        // 获取选中年级下的所有班级
        const { classesByGrade } = Utils.getGradesAndClasses(filteredData);
        const classes = classesByGrade[this.filter.grade] || [];

        // 更新班级选项
        Utils.updateSelectOptions(classFilter, classes, '全部班级');

        // 重置班级筛选器的值
        classFilter.value = this.filter.class || '';
    },
    
    /**
     * 根据筛选条件过滤数据
     * @returns {Array} - 过滤后的数据
     */
    getFilteredData: function() {
        return this.data.filter(item => {
            // 学校筛选
            if (this.filter.school && item.school_name !== this.filter.school) {
                return false;
            }

            // 年级筛选 - 比较中文年级显示
            if (this.filter.grade && this.filter.grade !== '') {
                const gradeText = Utils.numberToGrade(item.grade);
                if (gradeText !== this.filter.grade) {
                    return false;
                }
            }

            // 班级筛选 - 将两边都转为字符串进行比较
            if (this.filter.class && String(item.class) !== String(this.filter.class)) {
                return false;
            }

            // 搜索筛选
            if (this.filter.search && !String(item.name).toLowerCase().includes(this.filter.search.toLowerCase())) {
                return false;
            }

            return true;
        });
    },
    
    /**
     * 渲染打字数据表格
     */
    renderData: function() {
        const filteredData = this.getFilteredData();
        const tbody = document.getElementById('typing-data');

        if (filteredData.length === 0) {
            tbody.innerHTML = `<tr><td colspan="5" class="no-data">没有找到相关数据</td></tr>`;
            return;
        }

        // 为确保调试信息可见，先在控制台打印一些关键数据
        console.log('渲染的数据条数:', filteredData.length);
        console.log('第一条数据示例:', filteredData[0]);

        tbody.innerHTML = filteredData.map(item => {
            // 确保ID使用student_identifier
            const id = item.student_identifier;
            if (!id) {
                console.error('警告: 学生数据缺少ID', item);
                return '';
            }

            return `
                <tr data-id="${id}" data-student-id="${id}">
                    <td>${item.school_name || '未知学校'}</td>
                    <td>${Utils.numberToGrade(item.grade)}</td>
                    <td>${item.class}</td>
                    <td style="white-space: nowrap;">${item.name}</td>
                    <td class="typing-speed">${item.best_speed || 0}</td>
                </tr>
            `;
        }).join('');
    },
    


    /**
     * 更新排行榜
     */
    updateRanking: function() {
        // 获取图表容器元素
        const chartContainer = document.querySelector('.chart-container');
        
        // 根据排行类型更新对应的排行榜
        switch (this.rankingType) {
            case 'individual':
                this.updateIndividualRanking();
                // 在个人排行模式下显示图表
                if (chartContainer) {
                    chartContainer.style.display = 'block';
                }
                break;
            case 'progress':
                this.updateProgressRanking();
                // 在进步之星模式下隐藏图表
                if (chartContainer) {
                    chartContainer.style.display = 'none';
                }
                break;
            default:
                this.updateIndividualRanking();
                if (chartContainer) {
                    chartContainer.style.display = 'block';
                }
        }
    },

    /**
     * 更新个人排行榜
     */
    updateIndividualRanking: function() {
        const rankingDiv = document.getElementById('typing-ranking');
        
        // 按年级分组并按打字速度从高到低排序
        const gradeGroups = {};
        
        // 首先按年级对学生进行分组
        this.data.forEach(student => {
            if (!gradeGroups[student.grade]) {
                gradeGroups[student.grade] = [];
            }
            gradeGroups[student.grade].push(student);
        });
        
        // 对每个年级组内的学生按打字速度排序
        for (const grade in gradeGroups) {
            gradeGroups[grade].sort((a, b) => b.best_speed - a.best_speed);
        }
        
        // 如果没有数据，显示提示信息
        if (Object.keys(gradeGroups).length === 0) {
            rankingDiv.innerHTML = '<div class="no-data">暂无排名数据</div>';
            this.createChart({
                labels: [],
                data: [],
                title: '个人打字速度排行'
            });
            return;
        }
        
        // 获取排行榜中要显示的年级，优先使用筛选器中选择的年级
        let selectedGrade = this.filter.grade && gradeGroups[this.filter.grade] 
            ? this.filter.grade 
            : Object.keys(gradeGroups)[0];
        
        // 添加年级选择器到顶部
        let html = `
            <div class="grade-selector">
                <label>选择年级：</label>
                <select id="typing-ranking-grade-selector">
                    ${Object.keys(gradeGroups).map(grade => {
                        // 将数字年级转换为文字格式 (如 1 -> 一年级)
                        const displayGrade = typeof Utils !== 'undefined' && Utils.numberToGrade ? 
                            Utils.numberToGrade(grade) : grade + '年级';
                        return `<option value="${grade}" ${grade === selectedGrade ? 'selected' : ''}>${displayGrade}</option>`;
                    }).join('')}
                </select>
            </div>
        `;
        
        // 构建所选年级的排行榜HTML
        const topStudents = gradeGroups[selectedGrade].slice(0, 10);
        const chartLabels = [];
        const chartData = [];
        
        // 如果该年级有学生数据，显示该年级的排行榜
        if (topStudents.length > 0) {
            // 将数字年级转换为文字格式 (如 1 -> 一年级)
            const displayGrade = typeof Utils !== 'undefined' && Utils.numberToGrade ? 
                Utils.numberToGrade(selectedGrade) : selectedGrade + '年级';
            
            // 添加排行榜容器
            html += `
                <div class="ranking-section" id="typing-grade-ranking">
                    <h4>${displayGrade}排行榜 (前10名)</h4>
                    ${topStudents.map((student, index) => {
                        // 只取前三名的数据添加到图表中
                        if (index < 3) {
                            chartLabels.push(`${student.class} ${student.name}`);
                            chartData.push(student.best_speed);
                        }
                        
                        return `
                            <div class="ranking-item ${this.getRankClass(index)}">
                                <div>
                                    <span class="rank">&nbsp;</span>
                                    <span>${student.class} ${student.name}</span>
                                </div>
                                <div>${student.best_speed}字/分钟</div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        } else {
            html += `<div class="no-data" id="typing-grade-ranking">该年级暂无排名数据</div>`;
        }
        
        // 显示排行榜
        rankingDiv.innerHTML = html;
        
        // 更新图表（只显示所选年级前三名）
        this.createChart({
            labels: chartLabels,
            data: chartData,
            title: `${typeof Utils !== 'undefined' && Utils.numberToGrade ? Utils.numberToGrade(selectedGrade) : selectedGrade + '年级'}打字速度排行(前三名)`
        });
        
        // 绑定年级选择器的变更事件，直接使用闭包避免重复绑定事件
        document.getElementById('typing-ranking-grade-selector').addEventListener('change', (e) => {
            const newSelectedGrade = e.target.value;
            
            // 切换排行榜时重用gradeGroups数据，不需要重新计算
            this.updateGradeRankingContent(newSelectedGrade, gradeGroups);
        });
    },

    /**
     * 更新年级排行榜内容，复用已有分组数据
     * @param {string} selectedGrade - 选中的年级
     * @param {Object} gradeGroups - 按年级分组的学生数据
     */
    updateGradeRankingContent: function(selectedGrade, gradeGroups) {
        const topStudents = gradeGroups[selectedGrade].slice(0, 10);
        const chartLabels = [];
        const chartData = [];
        
        // 构建新的排行榜HTML
        let rankingHTML = '';
        
        // 将数字年级转换为文字格式 (如 1 -> 一年级)
        const displayGrade = typeof Utils !== 'undefined' && Utils.numberToGrade ? 
            Utils.numberToGrade(selectedGrade) : selectedGrade + '年级';
        
        if (topStudents.length > 0) {
            rankingHTML = `
                <h4>${displayGrade}排行榜 (前10名)</h4>
                ${topStudents.map((student, index) => {
                    // 只取前三名的数据添加到图表中
                    if (index < 3) {
                        chartLabels.push(`${student.class} ${student.name}`);
                        chartData.push(student.best_speed);
                    }
                    
                    return `
                        <div class="ranking-item ${this.getRankClass(index)}">
                            <div>
                                <span class="rank">&nbsp;</span>
                                <span>${student.class} ${student.name}</span>
                            </div>
                            <div>${student.best_speed}字/分钟</div>
                        </div>
                    `;
                }).join('')}
            `;
        } else {
            rankingHTML = `<div class="no-data">该年级暂无排名数据</div>`;
        }
        
        // 更新排行榜内容
        const rankingSection = document.getElementById('typing-grade-ranking');
        if (rankingSection) {
            rankingSection.innerHTML = rankingHTML;
        }
        
        // 更新图表
        this.createChart({
            labels: chartLabels,
            data: chartData,
            title: `${displayGrade}打字速度排行(前三名)`
        });
        
        // 保持下拉框选中状态
        document.getElementById('typing-ranking-grade-selector').value = selectedGrade;
    },

    /**
     * 创建图表
     * @param {Object} chartData - 图表数据对象
     */
    createChart: function(chartData) {
        const ctx = document.getElementById('typing-chart').getContext('2d');
        
        // 如果已有图表实例，销毁它
        if (this.chart) {
            this.chart.destroy();
        }
        
        // 为前三名使用特殊颜色
        const backgroundColors = chartData.data.map((_, index) => {
            if (index === 0) return 'rgba(255, 215, 0, 0.7)';  // 金色
            if (index === 1) return 'rgba(192, 192, 192, 0.7)'; // 银色
            if (index === 2) return 'rgba(205, 127, 50, 0.7)';  // 铜色
            return 'rgba(54, 162, 235, 0.6)'; // 默认蓝色
        });
        
        // 创建数据集
        const datasets = [{
            label: chartData.title,
            data: chartData.data,
            backgroundColor: backgroundColors,
            borderColor: backgroundColors.map(color => color.replace('0.6', '1').replace('0.7', '1')),
            borderWidth: 1,
            yAxisID: 'y'
        }];
        
        // 如果有次要数据，添加到数据集
        if (chartData.secondaryData && chartData.secondaryLabel) {
            datasets.push({
                label: chartData.secondaryLabel,
                data: chartData.secondaryData,
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1,
                type: 'line',
                yAxisID: 'y1'
            });
        }
        
        // 创建图表配置
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000, // 动画持续时间（毫秒）
                easing: 'easeOutQuart' // 缓动函数
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: chartData.secondaryData ? '主数据' : '打字速度（字/分钟）'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.raw;
                            return `${label}: ${value}`;
                        }
                    }
                }
            }
        };
        
        // 如果有次要数据，添加第二个Y轴
        if (chartData.secondaryData) {
            chartOptions.scales.y1 = {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                grid: {
                    drawOnChartArea: false // 只显示次要Y轴的刻度线
                },
                title: {
                    display: true,
                    text: chartData.secondaryLabel || '次要数据'
                }
            };
        }
        
        // 创建图表
        this.chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: chartData.labels,
                datasets: datasets
            },
            options: chartOptions
        });
    },
    
    /**
     * 导出数据为Excel
     */
    handleExport: function() {
        Utils.showLoading(true);
        
        setTimeout(() => {
            try {
                // 获取当前筛选后的数据
                const filteredData = this.getFilteredData();
                
                // 转换为Excel友好格式
                const excelData = filteredData.map(item => ({
                    '年级': item.grade,
                    '班级': item.class,
                    '姓名': item.name,
                    '中文速度(字/分钟)': item.language === 'zh' ? item.best_speed : '',
                    '英文速度(字/分钟)': item.language === 'en' ? item.best_speed : '',
                    '更新时间': item.record_date ? new Date(item.record_date).toLocaleString() : ''
                }));
                
                // 导出Excel
                Utils.exportToExcel(excelData, '打字数据');
                
                Utils.showMessage('打字数据导出成功');
            } catch (error) {
                console.error('导出打字数据失败:', error);
                Utils.showMessage('导出失败，请重试');
            } finally {
                Utils.showLoading(false);
            }
        }, 300);
    },
    
    /**
     * 导入打字数据
     */
    handleImport: async function() {
        try {
            const importedData = await Utils.importFromExcel();
            if (!importedData || importedData.length === 0) {
                return;
            }
            
            Utils.showLoading(true);
            
            let successCount = 0;
            let failCount = 0;
            
            // 逐条处理导入的数据
            for (const row of importedData) {
                try {
                    // 验证必要字段
                    if (!row['年级'] || !row['班级'] || !row['姓名']) {
                        failCount++;
                        continue;
                    }
                    
                    // 构造学生ID
                    const studentId = `${row['年级']}_${row['班级']}_${row['姓名']}`;
                    
                    // 处理中文速度
                    if (row['中文速度(字/分钟)'] !== undefined && row['中文速度(字/分钟)'] !== '') {
                        const zhSpeed = parseInt(row['中文速度(字/分钟)']) || 0;
                        if (zhSpeed > 0) {
                            // 使用数据库API添加打字记录
                            await DB.addTypingRecord({
                                student_identifier: studentId,
                                speed: zhSpeed,
                                accuracy: 100, // 假设准确率为100%
                                language: 'zh'
                            });
                        }
                    }
                    
                    // 处理英文速度
                    if (row['英文速度(字/分钟)'] !== undefined && row['英文速度(字/分钟)'] !== '') {
                        const enSpeed = parseInt(row['英文速度(字/分钟)']) || 0;
                        if (enSpeed > 0) {
                            // 使用数据库API添加打字记录
                            await DB.addTypingRecord({
                                student_identifier: studentId,
                                speed: enSpeed,
                                accuracy: 100, // 假设准确率为100%
                                language: 'en'
                            });
                        }
                    }
                    
                    successCount++;
                } catch (error) {
                    console.error('导入打字数据行失败:', error, row);
                    failCount++;
                }
            }
            
            // 重新加载数据
            await this.loadData();
            
            Utils.showMessage(`导入完成。成功: ${successCount}，失败: ${failCount}`);
        } catch (error) {
            console.error('导入打字数据失败:', error);
            Utils.showMessage('导入失败，请重试');
        } finally {
            Utils.showLoading(false);
        }
    },

    /**
     * 重置筛选器
     */
    resetFilters: function() {
        // 重置筛选条件对象
        this.filter.school = '';
        this.filter.grade = '';
        this.filter.class = '';
        this.filter.search = '';

        // 重置UI上的筛选控件
        document.getElementById('typing-school-filter').value = '';
        document.getElementById('typing-search').value = '';

        // 重置筛选器状态
        this.initializeFilterStates();

        // 重新渲染数据
        this.renderData();

        // 显示提示信息
        Utils.showMessage('筛选条件已重置');
    },

    /**
     * 更新进步之星排行榜
     */
    updateProgressRanking: function() {
        const rankingDiv = document.getElementById('typing-ranking');
        
        if (!rankingDiv) {
            console.error('找不到排行榜容器元素');
            return;
        }
        
        // 检查是否有进步数据
        if (!this.progressData || this.progressData.length === 0) {
            rankingDiv.innerHTML = '<div class="no-data">暂无进步数据</div>';
            return;
        }
        
        // 按年级分组
        const gradeGroups = {};
        
        // 首先按年级对学生进行分组
        this.progressData.forEach(student => {
            if (!gradeGroups[student.grade]) {
                gradeGroups[student.grade] = [];
            }
            gradeGroups[student.grade].push(student);
        });
        
        // 如果没有数据，显示提示信息
        if (Object.keys(gradeGroups).length === 0) {
            rankingDiv.innerHTML = '<div class="no-data">暂无进步数据</div>';
            return;
        }
        
        // 获取排行榜中要显示的年级，优先使用筛选器中选择的年级
        let selectedGrade = this.filter.grade && gradeGroups[this.filter.grade] 
            ? this.filter.grade 
            : Object.keys(gradeGroups)[0];
        
        // 添加年级选择器到顶部
        let html = `
            <div class="grade-selector">
                <label>选择年级：</label>
                <select id="typing-progress-grade-selector">
                    ${Object.keys(gradeGroups).map(grade => {
                        // 将数字年级转换为文字格式 (如 1 -> 一年级)
                        const displayGrade = typeof Utils !== 'undefined' && Utils.numberToGrade ? 
                            Utils.numberToGrade(grade) : grade + '年级';
                        return `<option value="${grade}" ${grade === selectedGrade ? 'selected' : ''}>${displayGrade}</option>`;
                    }).join('')}
                </select>
            </div>
        `;
        
        // 构建所选年级的排行榜HTML
        const topStudents = gradeGroups[selectedGrade].slice(0, 10);
        
        // 如果该年级有学生数据，显示该年级的排行榜
        if (topStudents.length > 0) {
            // 将数字年级转换为文字格式 (如 1 -> 一年级)
            const displayGrade = typeof Utils !== 'undefined' && Utils.numberToGrade ? 
                Utils.numberToGrade(selectedGrade) : selectedGrade + '年级';
            
            // 添加排行榜容器
            html += `
                <div class="ranking-section" id="typing-progress-ranking">
                    <h4>${displayGrade}进步之星 (前10名)</h4>
                    ${topStudents.map((student, index) => {
                        // 显示进步值，添加加号
                        const progressText = `+${student.progress}`;
                        
                        return `
                            <div class="ranking-item ${this.getRankClass(index)}">
                                <div>
                                    <span class="rank">&nbsp;</span>
                                    <span>${student.class} ${student.name}</span>
                                    <small>(${student.previous_speed}→${student.latest_speed})</small>
                                </div>
                                <div class="positive-progress">${progressText}字/分钟</div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        } else {
            html += `<div class="no-data" id="typing-progress-ranking">该年级暂无进步数据</div>`;
        }
        
        // 显示排行榜
        rankingDiv.innerHTML = html;
        
        // 绑定年级选择器的变更事件
        const gradeSelector = document.getElementById('typing-progress-grade-selector');
        if (gradeSelector) {
            gradeSelector.addEventListener('change', (e) => {
                const newSelectedGrade = e.target.value;
                
                // 切换排行榜时重用gradeGroups数据，不需要重新计算
                this.updateProgressRankingContent(newSelectedGrade, gradeGroups);
            });
        }
    },

    /**
     * 更新进步之星排行榜内容，复用已有分组数据
     * @param {string} selectedGrade - 选中的年级
     * @param {Object} gradeGroups - 按年级分组的学生数据
     */
    updateProgressRankingContent: function(selectedGrade, gradeGroups) {
        const rankingSection = document.getElementById('typing-progress-ranking');
        if (!rankingSection) {
            console.error('找不到进步排行榜容器元素');
            return;
        }
        
        const topStudents = gradeGroups[selectedGrade].slice(0, 10);
        
        // 构建新的排行榜HTML
        let rankingHTML = '';
        
        // 将数字年级转换为文字格式 (如 1 -> 一年级)
        const displayGrade = typeof Utils !== 'undefined' && Utils.numberToGrade ? 
            Utils.numberToGrade(selectedGrade) : selectedGrade + '年级';
        
        if (topStudents.length > 0) {
            rankingHTML = `
                <h4>${displayGrade}进步之星 (前10名)</h4>
                ${topStudents.map((student, index) => {
                    // 显示进步值，添加加号
                    const progressText = `+${student.progress}`;
                    
                    return `
                        <div class="ranking-item ${this.getRankClass(index)}">
                            <div>
                                <span class="rank">&nbsp;</span>
                                <span>${student.class} ${student.name}</span>
                                <small>(${student.previous_speed}→${student.latest_speed})</small>
                            </div>
                            <div class="positive-progress">${progressText}字/分钟</div>
                        </div>
                    `;
                }).join('')}
            `;
        } else {
            rankingHTML = `<div class="no-data">该年级暂无进步数据</div>`;
        }
        
        // 更新排行榜内容
        rankingSection.innerHTML = rankingHTML;
        
        // 保持下拉框选中状态
        const gradeSelector = document.getElementById('typing-progress-grade-selector');
        if (gradeSelector) {
            gradeSelector.value = selectedGrade;
        }
    },

    /**
     * 初始化文章相关功能
     */
    initArticleFeatures: function() {
        // 关闭文章弹窗
        document.getElementById('close-article-modal').addEventListener('click', this.hideAddArticleModal.bind(this));
        document.getElementById('cancel-article').addEventListener('click', this.hideAddArticleModal.bind(this));
        
        // 保存文章
        document.getElementById('save-article').addEventListener('click', this.submitArticleForm.bind(this));
    },

    /**
     * 显示添加文章弹窗
     */
    showAddArticleModal: function() {
        this.resetArticleForm();
        document.getElementById('article-modal').style.display = 'block';
    },

    /**
     * 隐藏添加文章弹窗
     */
    hideAddArticleModal: function() {
        document.getElementById('article-modal').style.display = 'none';
    },

    /**
     * 验证文章表单
     * @returns {boolean} 表单是否有效
     */
    validateArticleForm: function() {
        const title = document.getElementById('article-title').value.trim();
        const content = document.getElementById('article-content').value.trim();
        const language = document.getElementById('article-language').value;
        const difficulty = document.getElementById('article-difficulty').value;
        
        if (!title) {
            Utils.showMessage('请输入文章标题', 'error');
            return false;
        }
        
        if (!content) {
            Utils.showMessage('请输入文章内容', 'error');
            return false;
        }
        
        if (!language) {
            Utils.showMessage('请选择文章语言', 'error');
            return false;
        }
        
        if (!difficulty) {
            Utils.showMessage('请选择文章难度', 'error');
            return false;
        }
        
        return true;
    },

    /**
     * 提交文章表单
     */
    submitArticleForm: function() {
        if (!this.validateArticleForm()) {
            return;
        }
        
        // 显示加载状态
        Utils.showLoading(true);
        
        // 获取表单数据
        const title = document.getElementById('article-title').value.trim();
        const content = document.getElementById('article-content').value.trim();
        const language = document.getElementById('article-language').value;
        const difficulty = document.getElementById('article-difficulty').value;
        const gradeLevel = document.getElementById('article-grade').value.trim();
        
        // 构建请求数据
        const requestData = {
            title,
            content,
            language,
            difficulty: parseInt(difficulty),
            grade_level: gradeLevel || null
        };
        
        // 调用API保存文章
        fetch('/api/articles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Utils.showMessage('文章添加成功', 'success');
                this.hideAddArticleModal();
            } else {
                throw new Error(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('添加文章失败:', error);
            Utils.showMessage('添加文章失败: ' + error.message, 'error');
        })
        .finally(() => {
            Utils.showLoading(false);
        });
    },

    /**
     * 重置文章表单
     */
    resetArticleForm: function() {
        document.getElementById('article-title').value = '';
        document.getElementById('article-content').value = '';
        document.getElementById('article-language').value = 'zh';
        document.getElementById('article-difficulty').value = '3';
        document.getElementById('article-grade').value = '';
    }
};

// 将TypingManager暴露到全局作用域，供index.html中的事件绑定使用
window.TypingManager = TypingManager;