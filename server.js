/**
 * 班级成绩管理系统 - 服务器入口文件
 * 
 * 这个文件是应用程序的主入口点，负责：
 * 1. 配置Express应用
 * 2. 连接数据库
 * 3. 设置中间件
 * 4. 配置路由
 * 5. 启动服务器
 */

// 导入依赖
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// 创建Express应用
const app = express();

// 直接加载数据库和路由
const db = require('./config/db');
const apiRoutes = require('./routes/api');
console.log('数据库和路由加载成功');

const PORT = process.env.PORT || 3001;  // 使用环境变量端口，Vercel需要这个

// 在Vercel环境中不创建日志目录（只读文件系统）
const logsDir = path.join(__dirname, 'logs');
let canWriteLogs = false;

if (!process.env.VERCEL) {
  // 只在非Vercel环境下创建日志目录
  try {
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    canWriteLogs = true;
  } catch (error) {
    console.log('无法创建日志目录，将使用控制台日志:', error.message);
    canWriteLogs = false;
  }
}

// 配置中间件
app.use((req, res, next) => {
  req.startTime = Date.now();
  
  // 在请求完成后记录响应时间
  res.on('finish', () => {
    const duration = Date.now() - req.startTime;
    
    // 如果请求时间超过500ms，记录为慢请求
    if (duration > 500) {
      const logMessage = `[${new Date().toISOString()}] ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms\n`;

      if (canWriteLogs) {
        try {
          fs.appendFileSync(path.join(logsDir, 'slow-requests.log'), logMessage);
        } catch (error) {
          console.log('无法写入日志文件:', error.message);
        }
      }

      console.log(`慢请求: ${req.method} ${req.url} - ${duration}ms`);
    }
  });
  
  next();
});

app.use(cors()); // 允许跨域请求
app.use(express.json()); // 处理JSON请求体
app.use(express.urlencoded({ extended: true })); // 处理URL编码的请求体

// 只记录错误请求
app.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    if (res.statusCode >= 400) {
      console.log(`❌ ${req.method} ${req.url} - ${res.statusCode}`);
    }
    return originalSend.call(this, data);
  };
  next();
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// API路由（必须在页面路由之前）
app.use('/api', require('./routes/api'));
app.use('/api/teacher', require('./routes/teacher'));

// 学生相关路由（放在前面，避免被其他路由拦截）
app.get('/studentsign', (req, res) => {
  console.log('访问学生签到界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'student.html'));
});

app.get('/stusign', (req, res) => {
  console.log('访问学生界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'student.html'));
});

// 更具体的路由必须放在更通用的路由之前
app.get('/student/stutype', (req, res) => {
  console.log('访问学生打字练习界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'stu-type.html'));
});

app.get('/stutype', (req, res) => {
  console.log('访问学生打字测试界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'stu-type.html'));
});

app.get('/student', (req, res) => {
  console.log('访问学生界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'student.html'));
});

// 页面路由处理
app.get('/', (req, res) => {
  console.log('访问主页:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/teacher/gl', (req, res) => {
  console.log('访问教师管理界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'teacher-new.html'));
});

app.get('/gl', (req, res) => {
  console.log('访问管理员界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'gl.html'));
});

// 教师相关路由（具体路由必须在通用路由之前）
// 新版教师管理页面
app.get('/teacher/new', (req, res) => {
  console.log('访问新版教师管理界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'teacher-new.html'));
});

app.get('/teacher/medal', (req, res) => {
  console.log('访问奖章管理界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/teacher/type', (req, res) => {
  console.log('访问打字管理界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 通用教师路由（必须放在最后）
app.get('/teacher', (req, res) => {
  console.log('访问教师界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 旧版教师管理路由已移除，重定向在上面处理

// 系统管理员路由
app.get('/gl', (req, res) => {
  console.log('访问系统管理员界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 教师注册页面
app.get('/reg', (req, res) => {
  console.log('访问教师注册界面:', req.url);
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 兼容旧路由（重定向到新路由）
app.get('/medal', (req, res) => {
  res.redirect('/teacher/medal');
});

app.get('/type', (req, res) => {
  res.redirect('/teacher/type');
});

// 简单的健康检查端点（不依赖数据库）
app.get('/api/ping', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    env: {
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: process.env.VERCEL,
      SUPABASE_URL: process.env.SUPABASE_URL ? 'configured' : 'missing',
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing'
    }
  });
});

// 数据库连接诊断端点
app.get('/api/db-test', async (req, res) => {
  try {
    console.log('开始数据库诊断...');

    // 尝试加载数据库模块
    const db = require('./config/db');

    // 测试基本连接
    const { data, error } = await db.supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('数据库查询失败:', error);
      return res.status(500).json({
        status: 'error',
        message: '数据库查询失败',
        error: {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        },
        timestamp: new Date().toISOString()
      });
    }

    // 测试表是否存在
    const { data: tableData, error: tableError } = await db.supabase
      .from('users')
      .select('*')
      .limit(1);

    res.json({
      status: 'success',
      message: '数据库连接正常',
      details: {
        connection: 'ok',
        usersTable: tableError ? 'error' : 'exists',
        tableError: tableError?.message || null
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('数据库诊断失败:', error);
    res.status(500).json({
      status: 'error',
      message: '数据库连接失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 创建默认管理员账户端点
app.post('/api/create-admin', async (req, res) => {
  try {
    const db = require('./config/db');
    const bcrypt = require('bcryptjs');

    // 检查是否已存在admin用户
    const { data: existingAdmin, error: checkError } = await db.supabase
      .from('users')
      .select('username')
      .eq('username', 'admin');

    if (checkError) {
      throw checkError;
    }

    if (existingAdmin && existingAdmin.length > 0) {
      return res.json({
        status: 'exists',
        message: '管理员账户已存在',
        timestamp: new Date().toISOString()
      });
    }

    // 创建管理员账户
    const hashedPassword = await bcrypt.hash('admin123', 10);

    const { data: newAdmin, error: insertError } = await db.supabase
      .from('users')
      .insert({
        username: 'admin',
        password: hashedPassword,
        display_name: 'Administrator',
        role: 'admin',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      throw insertError;
    }

    res.json({
      status: 'created',
      message: '管理员账户创建成功',
      data: {
        username: newAdmin.username,
        display_name: newAdmin.display_name,
        role: newAdmin.role
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('创建管理员账户失败:', error);
    res.status(500).json({
      status: 'error',
      message: '创建管理员账户失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API路由已在上面配置，移除重复配置

// 添加对/student.js的日志记录
app.get('/js/student.js', (req, res) => {
    console.log('请求获取student.js文件');
    const filePath = path.join(__dirname, 'public', 'js', 'student.js');
    res.sendFile(filePath);
});

// 启动服务器（仅在非Vercel环境中）
if (process.env.NODE_ENV !== 'production' || !process.env.VERCEL) {
  app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log('-----------------------------------');
    console.log('可用的页面:');
    console.log(`教师端: http://localhost:${PORT}/index.html`);
    console.log(`教师管理: http://localhost:${PORT}/teacher/gl`);
    console.log(`学生端: http://localhost:${PORT}/student.html`);
    console.log('-----------------------------------');
    console.log('可用的API:');
    console.log(`API测试: http://localhost:${PORT}/api/test`);
    console.log(`学生列表: http://localhost:${PORT}/api/students`);
    console.log('-----------------------------------');
    console.log('数据库: Supabase PostgreSQL');
    console.log('部署平台: Vercel');
    console.log('-----------------------------------');
  });
}

// 导出app供Vercel使用
module.exports = app;

// 错误处理中间件
app.use((err, req, res, next) => {
  const errorMsg = `[${new Date().toISOString()}] ${err.stack}`;
  console.error(errorMsg);

  // 在Vercel环境中只记录到控制台，不写入文件
  if (!process.env.VERCEL) {
    try {
      fs.appendFileSync(
        path.join(logsDir, 'errors.log'),
        `${errorMsg}\n`
      );
    } catch (writeError) {
      console.error('无法写入错误日志文件:', writeError.message);
    }
  }

  res.status(500).json({
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 处理404
app.use((req, res) => {
  res.status(404).json({ error: '请求的资源不存在' });
});

// 导出app供Vercel使用
module.exports = app;